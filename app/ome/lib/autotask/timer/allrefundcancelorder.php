<?php

/**
 *
 * Class ome_autotask_timer_transferorderpull
 */
class ome_autotask_timer_allrefundcancelorder
{
    /**
     * 全额退款，但是订单是未确认状态的，需要调用取消订单
     * @param $params
     * @param string $error_msg
     */
    public function process($params, &$error_msg = '')
    {
        $cache_key = "timer_allrefundcancelorder";
        //加个缓存，别执行的太频繁了
        $have_cache = cachecore::fetch($cache_key);
        if ($have_cache) {
            return;
        }
        cachecore::store($cache_key, 'runing', 600);

        //查找7天前 到 当前时间两小时前的
        //获取近7天的发货的订单
        $min_time = strtotime('today') - 604800;
        //当前时间
        $max_time = time();

        $sql = "select order_bn,order_id from sdb_ome_orders where process_status != 'cancel' and pay_status = '5' and is_fail = 'false' and last_modified > $min_time and last_modified < $max_time ";
        $db = kernel::database();
        $list = $db->select($sql);
        $order_mdl = app::get('ome')->model('orders');
        foreach ($list as $info) {
            $order_id = $info['order_id'];
            $order_mdl->cacnel($order_id,'全额退款，订单未取消订单,自动取消订单');
        }
        return true;
    }
}