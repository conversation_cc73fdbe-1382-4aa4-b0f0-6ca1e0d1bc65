<?PHP
/**
 * 每小时执行退款重试
 *
 * <AUTHOR>
 * @version 0.1
 */

class ome_autotask_timer_refundapplyretry
{
    public function process($params, &$error_msg=''){
        @ini_set('memory_limit','1024M');
        set_time_limit(0);
        ignore_user_abort(1);
        $refundApplyMdl = app::get("ome")->model("refund_apply");
        $start_time = strtotime(date("Y-m-d"));
        $end_time = $start_time+86400;
        $filter = array(
            'status' => ['5','6'],
            'create_time|between' => array($start_time, $end_time),
        );
        $rList = $refundApplyMdl->getList("apply_id", $filter, 0 ,1000);
        $apply_ids = array_column((array)$rList, "apply_id");
        if(!empty($apply_ids)){
            $this->doBatchAgreeRefund($apply_ids);
        }
        return true;
    }

    /**
     * 处理批量同意退款
     */
    public function doBatchAgreeRefund($apply_ids)
    {
        $data = [];
        $data['pay_type'] = 'online';#默认在线支付
        $this->app = app::get("ome");
        $refund_apply_obj = $this->app->model('refund_apply');
        $refund_obj = $this->app->model('refunds');
        $order_obj = $this->app->model('orders');
        $shop_obj = $this->app->model('shop');
        $operation_log_obj = $this->app->model('operation_log');

        $refundApplyLib = kernel::single('ome_refund_apply');
        $esb_reshipObj = kernel::single('erpapi_sap_request_reship');
        $reshipItemsMdl = app::get('ome')->model('reship_items');
        $paymetsMdl = app::get('ome')->model('payments');
        $delivery_obj = app::get('ome')->model('delivery');
        $didMdl = app::get("ome")->model("delivery_items_detail");

        foreach ($apply_ids as $key => $apply_id) {
            $a_apply = $refund_apply_obj->refund_apply_detail($apply_id);
            // 只处理退款中、退款失败的
            if (!in_array($a_apply['status'], array('5', '6'))) {
                continue;
            }
            $err_msg = '';
            //如果是售前退款，判断是否存在未撤销的发货单
            $is_cancel_dly = false;
            if ($a_apply['refund_refer'] == '0' && $a_apply['bn']) {
                $delivery_ids = $delivery_obj->getDeliverIdByOrderId($a_apply['order_id'], true);
                if (!empty($delivery_ids)) {
                    $didFilter = array(
                        'bn' => $a_apply['bn'],
                        'delivery_id' => $delivery_ids,
                    );
                    $didList = $didMdl->getList("delivery_id", $didFilter);
                    $cancel_delivery_ids = array_column($didList, "delivery_id");
                    $cancel_delivery_list = $delivery_obj->getList("delivery_bn,delivery_id", array("delivery_id" => $cancel_delivery_ids));
                    $cancel_delivery_bn = array_column($cancel_delivery_list, "delivery_bn", "delivery_id");
                    foreach ($cancel_delivery_ids as $ck => $cv) {
                        $result = $delivery_obj->rebackDelivery($cv, '售前退款撤销发货单');
                        if (!$result) {
                            $msg = '退款申请单号：' . $a_apply['refund_apply_bn'] . ' 撤销发货单：' . $cancel_delivery_bn[$cv] . "失败";
                            continue 2;
                        }
                    }
                }
                $is_cancel_dly = true;
            }

            $is_archive = kernel::single('archive_order')->is_archive($a_apply['source']);

            if ($is_archive) {
                $order_obj = kernel::single('archive_interface_orders');
                $order_detail = $order_obj->getOrders(array('order_id' => $a_apply['order_id']), '*');
            } else {
                $order_detail = $order_obj->order_detail($a_apply['order_id']);
            }

            $shop_detail = $shop_obj->dump($order_detail['shop_id'], 'node_id,node_type');

            // 如果申请金额大于已付金额, 则退出
            $money = $a_apply['money'] - $a_apply['bcmoney'];
            if (round($money, 3) > round($order_detail['payed'], 3)) {
                $msg = '退款申请单号：' . $a_apply['refund_apply_bn'] . ' 退款申请金额' . $money . '大于订单上的余额' . $order_detail['payed'];
                continue;
            }

            //检查当前退款申请单是否允许请求退款接口
            $api_fail_flag = 'false'; //是否存在请求接口失败
            $api_refund_request = 'true'; //是否发起前端退款请求
            $checkParams = array(
                'orderInfo' => $order_detail,
                'applyInfo' => $a_apply,
                'is_archive' => $is_archive,
                'api_fail_flag' => $api_fail_flag,
                'api_refund_request' => $api_refund_request,
            );
            $refund_request = $refundApplyLib->checkBatchForRequest($checkParams, $err_msg);
            $paymet_info = $paymetsMdl->dump(['order_id' => $order_detail['order_id']]);
            if ($paymet_info) {
                $data['pay_type'] = $data['pay_type'] ? $data['pay_type'] : $paymet_info['pay_type'];
                $data['account'] = $data['account'] ? $data['account'] : $paymet_info['account'];
                $data['pay_account'] = $data['pay_account'] ? $data['pay_account'] : $paymet_info['pay_account'];
                $data['pay_type'] = $data['pay_type'] ? $data['pay_type'] : $paymet_info['pay_type'];
                $data['bank'] = $data['bank'] ? $data['bank'] : $paymet_info['bank'];
            }

            // 发起前端退款请求
            if ($refund_request) {

                if (!$data['pay_type']) {
                    $msg = '请选择付款类型。';
                    continue;
                }

                $data['pay_type'] = $data['pay_type'];
                $data['order_id'] = $a_apply['order_id'];
                $data['apply_id'] = $apply_id;
                $data['refund_bn'] = $a_apply['refund_apply_bn'];
                $data['bcmoney'] = $a_apply['bcmoney'];
                $data['money'] = $money;
                //检查当前订单的状态，标记天猫售前退款的取消发货标记
                if ($is_cancel_dly) {
                    $data['cancel_dly_status'] = 'SUCCESS';
                }
                if ($is_archive) {
                    $data['is_archive'] = 1;
                }

                if ($refund_obj->refund_request($data)) {
                    //$this->splash('success',$url,'退款请求发起成功');
                } else {
                    $msg = '退款申请单号：' . $a_apply['refund_apply_bn'] . ' 退款请求发起失败,请重试';
                    continue;
                }

            } else {

                // 查找本申请是否是与售后相关的，如果相关，则检查并回写数据
                $return_refund_obj = $this->app->model('return_refund_apply');
                $return_refund_info = $return_refund_obj->dump(array('refund_apply_id' => $apply_id));
                if ($return_refund_info['return_id']) {
                    $return_product_obj = $this->app->model('return_product');
                    $return_info = $return_product_obj->product_detail($return_refund_info['return_id']);
                    if (($return_info['refundmoney'] + $a_apply['money']) > $return_info['tmoney']) {
                        $msg = '申请退款金额大于售后的退款金额！';
                        continue;
                    }
                    $return_info['refundmoney'] = $return_info['refundmoney'] + $a_apply['money'];

                    $return_product_obj->save($return_info);

                    $operation_log_obj->write_log('return@ome', $return_info['return_id'], "售后退款成功。");
                } else {
                    if (empty($a_apply['reship_id']) && app::get('ome')->model('reship')->db_dump(['reship_bn' => $a_apply['refund_apply_bn'], 'is_check|noequal' => '5'], 'reship_id')) {
                        $msg = '该退款单存在退货单，不能完成';
                        continue;
                    }
                }

                // 更新订单信息
                $order_data = array();
                if (round($a_apply['money'], 3) == round($order_detail['payed'], 3)) {
                    $order_data['pay_status'] = 5;
                } else {
                    $order_data['pay_status'] = 4;
                    // 部分退款置异常，防止客服不看直接审核
                    if (!$is_archive) {
                        kernel::single('ome_order_abnormal')->abnormal_set($a_apply['order_id'], '订单未发货部分退款');
                    }
                }
                $order_data['order_id'] = $a_apply['order_id'];
                $order_data['payed'] = $order_detail['payed'] - ($a_apply['money'] - $a_apply['bcmoney']);
                $order_obj->save($order_data);
                $operation_log_obj->write_log('order_modify@ome', $order_data['order_id'], "退款成功，更新订单退款金额。");

                // 退款申请状态更新
                $apply_data = array();
                $apply_data['apply_id'] = $apply_id;
                $apply_data['status'] = 4;
                $apply_data['refunded'] = $a_apply['money'];
                $apply_data['last_modified'] = time();
                $apply_data['account'] = $data['account'];
                $apply_data['pay_account'] = $data['pay_account'];
                $apply_data['pay_type'] = $data['pay_type'];
                $apply_data['payment'] = $data['payment'];

                # 获取推送esb的商家收入和esb优惠金额
                $esb_data = kernel::single('ome_sap_sap')->_esb_refund_amount($apply_id);
                # 合并esb金额
                $apply_data = array_merge($apply_data, $esb_data);

                $refund_apply_obj->save($apply_data);
                $operation_log_obj->write_log('refund_apply@ome', $a_apply['apply_id'], "退款成功，更新退款申请状态。");

                # 推送sap
                if (empty($a_apply['reship_id'])) {
                    kernel::single('ome_sap_sap')->push_refund($apply_id, $a_apply['shop_type']);
                } else {
                    kernel::single('ome_sap_sap')->push_reship($a_apply['reship_id'], $a_apply['shop_type']);
                }

                //更新售后退款金额
                $addon = unserialize($a_apply['addon']);
                $return_id = intval($addon['return_id']);
                if (!empty($return_id)) {
                    $sql = "UPDATE `sdb_ome_return_product` SET `refundmoney`=IFNULL(`refundmoney`,0)+{$a_apply['money']} WHERE `return_id`='" . $return_id . "'";
                    kernel::database()->exec($sql);
                }

                // 生成退款单
                $refund_data = array();
                if ($a_apply['refund_apply_bn']) {
                    $refund_data['refund_bn'] = $a_apply['refund_apply_bn'];
                } else {
                    $refund_data['refund_bn'] = $refund_obj->gen_id();
                }
                $refund_data['order_id'] = $a_apply['order_id'];
                $refund_data['shop_id'] = $order_detail['shop_id'];
                $refund_data['account'] = $data['account'];
                $refund_data['bank'] = $data['bank'];
                $refund_data['pay_account'] = $a_apply['pay_account'];
                $refund_data['currency'] = $order_detail['currency'];
                $refund_data['money'] = $a_apply['money'];
                $refund_data['paycost'] = 0; //没有第三方费用
                $refund_data['cur_money'] = $a_apply['money']; //汇率计算 TODO:应该为汇率后的金额，暂时是人民币金额
                $refund_data['pay_type'] = $data['pay_type'];
                $refund_data['payment'] = $data['payment'];
                $paymethods = ome_payment_type::pay_type();
                $refund_data['paymethod'] = $paymethods[$refund_data['pay_type']];
                //Todo ：确认paymethod
                $opInfo = kernel::single('ome_func')->getDesktopUser();
                $refund_data['op_id'] = $opInfo['op_id'];

                $refund_data['t_ready'] = time();
                $refund_data['t_sent'] = time();
                $refund_data['status'] = "succ"; #支付状态
                $refund_data['memo'] = $a_apply['memo'];
                $refund_obj->save($refund_data);

                //更新订单支付状态
                if ($is_archive) {
                    kernel::single('archive_order_func')->update_order_pay_status($a_apply['order_id']);
                } else {
                    kernel::single('ome_order_func')->update_order_pay_status($a_apply['order_id']);
                }

                if ($a_apply["refund_refer"] == "1") {
                    //生成售后单
                    kernel::single('sales_aftersale')->generate_aftersale($apply_id, 'refund');
                }

                $operation_log_obj->write_log('refund_accept@ome', $refund_data['refund_id'], "退款成功，生成退款单" . $refund_data['refund_bn']);
                if (!empty($return_id)) {
                    $return_data = array('return_id' => $return_id, 'status' => '4', 'refundmoney' => $refund_data['money'], 'last_modified' => time());
                    $Oreturn_product = $this->app->model('return_product');
                    $Oreturn_product->update_status($return_data);
                }
            }
        }
        return true;
    }
}