<?php

/**
 * @user: 七月
 * @date: 2025/1/17
 * @desc: 部分退款、库存不足订单自动审核
 */
class ome_autotask_timer_autoauditbyunmyown
{
    //每次寻源记录数
    const LIMIT = 500;

    public function process($params = '', &$error_msg = '')
    {
        set_time_limit(3600);

        //执行标记，防止多次执行
        $key = __CLASS__;
        base_kvstore::instance('crontab')->fetch($key, $lastRunFlag);
        $lastRunFlag = 'false';
        $rs          = ['total' => 0, 'succ' => 0, 'fail' => 0];
        if ($lastRunFlag != 'true') {
            base_kvstore::instance('crontab')->store($key, 'true');

            $ordersMdl            = app::get('ome')->model('orders');
            $shopMdl              = app::get('ome')->model('shop');
            $basicMStockLib       = kernel::single('material_basic_material_stock');
            $basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');

            //排除手动审核
//            $flag = [
//                omeauto_auto_const::__STORE_CODE,//库存不足
//            ];
//            foreach ($flag as $fv) {
//                $flagWhere[] = " (auto_status & {$fv} != {$fv} )";
//            }
//            $flagWhere = implode(' OR ', $flagWhere);

            $filter    = [
                'is_fail'             => 'false',
                'pay_status'          => ['1', '4'],
                'ship_status'         => ['0', '2', '3'],
                'status'              => 'active',
                'process_status'      =>
                    [
                        0 => 'unconfirmed',
                        1 => 'confirmed',
                        2 => 'splitting',
                    ],
                'filter_sql'          => ' (op_id >= 0 OR group_id >= 0) ',
                'archive'             => 0,
                'createtime|bthan'    => time() - 5 * 24 * 60 * 60,
                'last_modified|sthan' => time() - 120, // 2分钟前的
                'paytime|bthan'       => strtotime("-3 months"), // 3个月内的
                // 'pause'               => 'false',
                'lack_type'           => '0',//排除门店缺货
            ];
            $db        = kernel::database();
            $totalSize = $ordersMdl->count($filter);
            $totalPage = ceil($totalSize / self::LIMIT);
            for ($page = 1; $page <= $totalPage; $page++) {
                $offset    = ($page - 1) * self::LIMIT;
                $orderList = $ordersMdl->getList('order_id,order_bn,pay_status,payed,shop_type,paytime,auto_status,shop_id', $filter, $offset, self::LIMIT, ' order_id ASC ');
                if ($orderList) {
                    foreach ($orderList as $v) {
                        if (empty($v['order_id'])) {
                            continue;
                        }

                        if (in_array($v['pay_status'], ['0', '5', '6'])) {
                            continue;
                        }

                        $shop                            = $shopMdl->dump($v['shop_id'], 'config');
                        $config                          = unserialize($shop['config']);
                        $second_check_order_amount_limit = $config['second_check_order_amount_limit'] ?? 50;
                        //部分退款金额剩下50元以下的不处理
                        if ($v['pay_status'] == '4' && $v['payed'] < $second_check_order_amount_limit) {
                            $items = $db->select("SELECT item_id,product_id,nums FROM sdb_ome_order_items WHERE order_id=" . $v['order_id'] . " AND `delete`='false'");
                            foreach ($items as $itemVal) {
                                $basicMStockLib->unfreeze($itemVal['product_id'], $itemVal['nums']);
                                $basicMStockFreezeLib->unfreeze($itemVal['product_id'], material_basic_material_stock_freeze::__ORDER, 0, $v['order_id'], '', material_basic_material_stock_freeze::__SHARE_STORE, $itemVal['nums']);
                            }
                            //订单取消后，清除订单级预占流水
                            $basicMStockFreezeLib->delOrderFreeze($v['order_id']);
                            $ordersMdl->update(['process_status' => 'cancel'], ['order_id' => $v['order_id']]);
                            continue;
                        }

                        // 判断是否为集运订单，如果是集运订单则不处理
                        $jyInfo = kernel::single('ome_bill_label')->getBillLabelInfo($v['order_id'], 'order', 'XJJY');
                        if (!empty($jyInfo)) {
                            // 跳过集运订单处理
                            continue;
                        }

                        //订单异常、暂停状态
                        $ordersMdl->update(['pause' => 'false', 'abnormal' => 'false'], ['order_id' => $v['order_id']]);

                        $result = $this->exec($v['order_id']);

                        $rs['total'] += 1;
                        $rs['succ']  += $result['succ'];
                        $rs['fail']  += $result['fail'];
                    }
                }
            }
            base_kvstore::instance('crontab')->store($key, 'false');
        }

        return $rs;
    }

    /**
     * Notes: 执行自动审核
     * User: 七月
     * DateTime: 2025/1/17 14:34
     * @param int $orderId
     * @return false|int[]|mixed|null
     */
    public function exec(int $orderId): mixed
    {
        if (empty($orderId)) {
            return false;
        }

        $params               = [];
        $params[]['orders'][] = $orderId;

        //开始自动确认
        $isAuto    = true;//系统自动审单标识
        $orderAuto = new omeauto_auto_combine($isAuto);
        $result    = $orderAuto->process($params);
        unset($params);
        return $result;
    }
}