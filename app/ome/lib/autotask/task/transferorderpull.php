<?php


class ome_autotask_task_transferorderpull
{
    public function process($params, &$error_msg=''){

        $info = json_decode($params['orderinfo'], true);
        if(!$info){
            return true;
        }

        kernel::single('base_customlog')->saveLog('kafka', ['pullinfo'=>$info],'transferorder');

        //当前订单时间减去 1
        $order_obj = app::get('ome')->model('orders');
        $order_bn = trim($info['order_bn']);
        $order = $order_obj->dump(['order_bn'=>$order_bn], 'outer_lastmodify');
        if ($order) {
            $order_obj->update(array('outer_lastmodify' => ($order['outer_lastmodify'] - 1)), ['order_bn' => $order_bn]);
        }

        $obj_syncorder = kernel::single("ome_syncorder");
        $rsp_data = kernel::single('erpapi_router_request')->set('shop', $info['shop_id'])->order_get_order_detial(trim($info['order_bn']));
        if ($rsp_data['rsp'] == 'succ') {
            $sdf_order = $rsp_data['data']['trade'];
            $res = $obj_syncorder->get_order_log($sdf_order, $info['shop_id'], $msg);
            kernel::single('base_customlog')->saveLog('kafka', ['res'=>$res],'transferorder');
        } else {
            $error_msg = $rsp_data['err_msg'] ? $rsp_data['err_msg'] : "同步订单失败。";
            kernel::single('base_customlog')->saveLog('kafka', ['errormsg'=>$error_msg],'transferorder');
        }

        return true;
    }
}

