<?php

/**
 * @Author: <EMAIL>
 * @Datetime: 2022/3/19
 * @Describe: 订单优惠明细处理类
 */
class ome_order_coupon
{
    /**
     * 重组优惠明细数据
     * @param $data 优惠明细数据
     * @param array $ext_data 扩展数据
     * @param string $shop_type 店铺类型
     * @return array
     */
    public function couponDataFormat($data, $ext_data = array(), $shop_type = '360buy')
    {
        switch ($shop_type) {
            case '360buy':
                $rs = $this->_JdCouponDataFormat($data, $ext_data);
                break;
            case 'xhs': // 小红书
                $rs = $this->_XhsCouponDataFormat($data, $ext_data);
                break;
            case 'wxshipin': // 微信小店
                $rs = $this->_WxshipinCouponDataFormat($data, $ext_data);
                break;
            case 'ecos.ecshopx': // 微商城
                $rs = $this->_EcshopxCouponDataFormat($data, $ext_data);
                break;
            default:
                $rs = array('rsp' => 'fail', 'msg' => '没有单据类型');
        }
        return $rs;
    }

    /**
     * 微商城优惠
     * @param $order_objects
     * @param $ext_data
     * @return mixed
     */
    public function _EcshopxCouponDataFormat($order_objects, $ext_data)
    {
        $coupon = $objectCouponData = array();
        //根据店铺取运营组织
        $shopInfo = kernel::single('ome_shop')->getRowByShopId($ext_data['shop_id']);
        $orgId = $shopInfo['org_id'];

        foreach ($order_objects as $object) {
            foreach ($object['order_items'] as $item) {
                # 如果不存在优惠，不处理
                if (empty($item['promotion_id'])) {
                    continue;
                }

                $promotion_id = json_decode($item['promotion_id'], true);
                # 活动列表
                $full_discount_list = $promotion_id['shop_discount_detail']['full_discount_info'] ?? [];
                # 优惠券列表
                $coupon_list = $promotion_id['shop_discount_detail']['coupon_info'] ?? [];

                # 活动列表处理
                if (!empty($full_discount_list)) {
                    foreach ($full_discount_list as $discount) {
                        # 平台承担金额
                        if (floatval($discount['share_discount_cost']['platform_cost']) > 0) {
                            $tmp_data = array(
                                'oid' => $object['oid'],
                                'platform_type' => 'platform_discount',
                                'type' => 'discount',
                                'sub_type' => $discount['campaign_sub_type'] ?? '0',  // 默认：0 店铺活动
                                'coupon_id' => $discount['campaign_id'],
                                'coupon_name' => $discount['campaign_name'],
                                'coupon_type' => $discount['campaign_type'],
                                'coupon_amount' => $discount['share_discount_cost']['platform_cost'],
                                'platform_cost' => $discount['share_discount_cost']['platform_cost'],
                                'total_amount' => $discount['share_discount_cost']['platform_cost'],
                                'shop_cost' => 0,
                                'num' => $item['quantity'],
                                'material_bn' => $item['bn'],
                                'create_time' => time(),
                                'source' => $ext_data['coupon_source'],
                            );
                            if (!empty($discount['bugetcode'])) {
                                $tmp_data['bugetcode'] = $discount['bugetcode'];
                            }
                            if (!empty($discount['po_code'])) {
                                $tmp_data['po_code'] = $discount['po_code'];
                            }
                            $coupon[] = $tmp_data;
                        }

                        # 商家承担金额
                        if (floatval($discount['share_discount_cost']['shop_cost']) > 0) {
                            $tmp_data = [
                                'oid' => $object['oid'],
                                'type' => 'discount',
                                'platform_type' => 'shop_discount',
                                'sub_type' => $discount['campaign_sub_type'] ?? '0', // 默认：0 店铺活动
                                'coupon_id' => $discount['campaign_id'],
                                'coupon_name' => $discount['campaign_name'],
                                'coupon_type' => $discount['campaign_type'],
                                'coupon_amount' => $discount['share_discount_cost']['shop_cost'],
                                'shop_cost' => $discount['share_discount_cost']['shop_cost'],
                                'total_amount' => $discount['share_discount_cost']['shop_cost'],
                                'platform_cost' => 0,
                                'num' => $item['quantity'],
                                'material_bn' => $item['bn'],
                                'create_time' => time(),
                                'source' => $ext_data['coupon_source'],
                            ];
                            if (!empty($discount['bugetcode'])) {
                                $tmp_data['bugetcode'] = $discount['bugetcode'];
                            }
                            if (!empty($discount['po_code'])) {
                                $tmp_data['po_code'] = $discount['po_code'];
                            }
                            $coupon[] = $tmp_data;
                        }
                    }
                }

                # 优惠券列表处理
                if (!empty($coupon_list)) {
                    foreach ($coupon_list as $discount) {
                        # 平台承担金额
                        if (floatval($discount['share_discount_cost']['platform_cost']) > 0) {
                            $tmp_data = [
                                'oid' => $object['oid'],
                                'type' => 'coupon',
                                'platform_type' => 'platform_discount',
                                'sub_type' => $discount['coupon_sub_type'] ?? '0', // 优惠券子类型：0-商城，1-aio，2-aio_pro
                                'coupon_id' => $discount['coupon_id'],
                                'coupon_name' => $discount['coupon_name'],
                                'coupon_type' => $discount['coupon_type'],
                                'coupon_meta_id' => $discount['coupon_meta_id'],
                                'coupon_amount' => $discount['share_discount_cost']['platform_cost'],
                                'platform_cost' => $discount['share_discount_cost']['platform_cost'],
                                'total_amount' => $discount['share_discount_cost']['platform_cost'],
                                'shop_cost' => 0,
                                'num' => $item['quantity'],
                                'material_bn' => $item['bn'],
                                'create_time' => time(),
                                'source' => $ext_data['coupon_source'],
                            ];
                            if (!empty($discount['bugetcode'])) {
                                $tmp_data['bugetcode'] = $discount['bugetcode'];
                            }
                            if (!empty($discount['po_code'])) {
                                $tmp_data['po_code'] = $discount['po_code'];
                            }
                            $coupon[] = $tmp_data;
                        }

                        # 商家承担金额
                        if (floatval($discount['share_discount_cost']['shop_cost']) > 0) {
                            $tmp_data = [
                                'oid' => $object['oid'],
                                'type' => 'coupon',
                                'platform_type' => 'shop_discount',
                                'sub_type' => $discount['coupon_sub_type'] ?? '0', // 优惠券子类型：0-商城，1-aio，2-aio_pro
                                'coupon_id' => $discount['coupon_id'],
                                'coupon_name' => $discount['coupon_name'],
                                'coupon_type' => $discount['coupon_type'],
                                'coupon_meta_id' => $discount['coupon_meta_id'],
                                'coupon_amount' => $discount['share_discount_cost']['shop_cost'],
                                'platform_cost' => 0,
                                'shop_cost' => $discount['share_discount_cost']['shop_cost'],
                                'total_amount' => $discount['share_discount_cost']['shop_cost'],
                                'num' => $item['quantity'],
                                'material_bn' => $item['bn'],
                                'create_time' => time(),
                                'source' => $ext_data['coupon_source'],
                            ];
                            if (!empty($discount['bugetcode'])) {
                                $tmp_data['bugetcode'] = $discount['bugetcode'];
                            }
                            if (!empty($discount['po_code'])) {
                                $tmp_data['po_code'] = $discount['po_code'];
                            }
                            $coupon[] = $tmp_data;
                        }
                    }
                }

                //优惠明细汇总
                $objectCouponData[] = array(
                    'order_bn' => $ext_data['order_bn'],
                    'num' => $item['quantity'],
                    'material_name' => $item['name'],
                    'material_bn' => $item['bn'],
                    'oid' => $object['oid'],
                    'create_time' => time(),
                    'shop_id' => $ext_data['shop_id'],
                    'shop_type' => $ext_data['shop_type'],
                    'source' => 'push',
                    'addon' => serialize($promotion_id),
                    'org_id' => $orgId,
                );
            }
        }

        $res = [];
        $res['coupon_data'] = $coupon;
        $res['objects_coupon_data'] = $objectCouponData;
        return $res;
    }

    /**
     * 微信小店优惠
     * @param $data
     * @param $ext_data
     */
    public function _WxshipinCouponDataFormat($order_objects, $ext_data)
    {
        $coupon = $objectCouponData = array();
        $split_result = ['total_amount' => 0, 'split_objects' => []];
        //根据店铺取运营组织
        $shopInfo = kernel::single('ome_shop')->getRowByShopId($ext_data['shop_id']);
        $orgId = $shopInfo['org_id'];
        foreach ($order_objects as $oov) {
            foreach ($oov['order_items'] as $oiv) {
                if (empty($oiv['extend_item_list'])) {
                    continue;
                }

                # 设置分摊sku明细
                if (floatval($oiv['divide_order_fee']) > 0) {
                    $split_result['total_amount'] = bcadd($split_result['total_amount'], $oiv['divide_order_fee'], 2);
                    $split_result['split_objects'][$oov['oid']] = [
                        'oid' => $oov['oid'],
                        'bn' => $oiv['bn'],
                        'quantity' => $oov['quantity'],
                        'divide_order_fee' => $oiv['divide_order_fee']
                    ];
                }

                # 商家承担优惠
                $merchant_coupon_list = $oiv['extend_item_list']['merchant_discounted_price'] ?? 0;
                if (floatval($merchant_coupon_list) >= 0) {
                    $item = array(
                        'oid' => $oov['oid'],
                        'platform_type' => 'shop_discount',
                        'type' => 'coupon',
                        'coupon_type' => 0,
                        'coupon_name' => '商家优惠',
                        'num' => $oov['quantity'],
                        'material_bn' => $oiv['bn'],
                        'coupon_id' => '',
                        'coupon_meta_id' => '',
                        'create_time' => time(),
                        'source' => $ext_data['coupon_source'],
                    );

                    $merchant_coupon_amount = bcdiv($merchant_coupon_list, 100, 2);
                    # 判断是否存在优惠券
                    if (!empty($oiv['extend_item_list']['order_product_coupon_info_list'])) {
                        foreach ($oiv['extend_item_list']['order_product_coupon_info_list'] as $v) {
                            if (in_array($v['coupon_type'], [1, 3])) {
                                $item['coupon_id'] = $v['user_coupon_id'];
                                # 优惠券id
                                if (!empty($v['coupon_id']) && $v['user_coupon_id'] != '0') {
                                    $item['coupon_meta_id'] = $v['coupon_id'];
                                }
                                # 平台优惠
                                if ($v['coupon_type'] == 3) {
                                    $item['coupon_type'] = $v['coupon_type'];
                                    $item['coupon_meta_id'] = $v['user_coupon_id'];
                                    $item['coupon_name'] = '平台优惠';
                                    $item['platform_type'] = 'platform_discount';
                                }
                                $item['coupon_amount'] = bcdiv($v['discounted_price'], 100, 2);
                                // 过滤优惠金额为0的优惠券
                                if(floatval($item['coupon_amount']) > 0){
                                    $coupon[] = $item;
                                }
                            }
                        }
                    } else {
                        # 设定用户优惠券ID
                        if (!empty($ext_data['coupon_info']) && isset($ext_data['coupon_info']['user_coupon_id'])) {
                            $item['coupon_id'] = $ext_data['coupon_info']['user_coupon_id'];
                        }
                        # 优惠券默认为商家优惠金额
                        $item['coupon_amount'] = $merchant_coupon_amount;
                        // 过滤优惠金额为0的优惠券
                        if(floatval($item['coupon_amount']) > 0){
                            $coupon[] = $item;
                        }
                    }
                }

                # 达人承担优惠
                $kol_coupon_list = $oiv['extend_item_list']['finder_discounted_price'] ?? 0;
                if (floatval($kol_coupon_list) > 0) {
                    $item = array(
                        'oid' => $oov['oid'],
                        'platform_type' => 'kol_discount',
                        'type' => 'coupon',
                        'coupon_type' => 2,
                        'coupon_name' => '达人优惠',
                        'num' => $oov['quantity'],
                        'material_bn' => $oiv['bn'],
                        'create_time' => time(),
                        'source' => $ext_data['coupon_source'],
                    );
                    $sku_discount_amount = 0;
                    $kol_coupon_amount = bcdiv($kol_coupon_list, 100, 2);
                    foreach ($oiv['extend_item_list']['order_product_coupon_info_list'] as $v) {
                        if ($v['coupon_type'] == 2) {
                            $item['coupon_id'] = $v['user_coupon_id'];
                            # 优惠券id
                            $item['coupon_meta_id'] = '0';
                            if (!empty($v['coupon_id']) && $v['user_coupon_id'] != '0') {
                                $item['coupon_meta_id'] = $v['coupon_id'];
                            }
                            $item['coupon_amount'] = bcdiv($v['discounted_price'], 100, 2);
                            # 记录sku的优惠金额
                            $sku_discount_amount = bcadd($sku_discount_amount, $item['coupon_amount'], 2);
                            $coupon[] = $item;
                        }
                    }
                    # 判断金额是否一致，视频号存在金额不一致的情况
                    if (bccomp($kol_coupon_amount, $sku_discount_amount, 2) > 0) {
                        # 尾差金额
                        $diff_amount = bcsub($kol_coupon_amount, $sku_discount_amount, 2);
                        foreach ($coupon as $k => $v) {
                            if ($v['shop_discount'] != 'kol_discount' && !empty($v['coupon_id'])) {
                                continue;
                            }
                            if (floatval($diff_amount) > 0) {
                                $coupon[$k]['coupon_amount'] = bcadd($v['coupon_amount'], $diff_amount, 2);
                                $diff_amount = 0;
                            }
                        }
                    }
                }

                # 商家改价 - sku级别
                $sku_adjust_amount = $value['extend_item_list']['change_price'] ?? 0;
                if (floatval($sku_adjust_amount) > 0) {
                    $coupon[] = array(
                        'oid' => $oov['oid'],
                        'platform_type' => 'shop_discount',
                        'type' => 'adjust',
                        'sub_type' => 'sku',
                        'coupon_name' => '商家sku改价',
                        'num' => $oov['quantity'],
                        'material_bn' => $oiv['bn'],
                        'coupon_amount' => bcdiv($sku_adjust_amount, 100, 2),
                        'create_time' => time(),
                        'source' => $ext_data['coupon_source'],
                    );
                }

                # 积分抵扣
                $deduction_amount = $value['extend_item_list']['deduction_price'] ?? 0;
                if (floatval($deduction_amount) > 0) {
                    $coupon[] = array(
                        'oid' => $oov['oid'],
                        'platform_type' => 'platform_discount',
                        'type' => 'deduction',
                        'coupon_type' => 1,
                        'coupon_name' => '积分抵扣',
                        'num' => $oov['quantity'],
                        'material_bn' => $oiv['bn'],
                        'coupon_amount' => bcdiv($deduction_amount, 100, 2),
                        'create_time' => time(),
                        'source' => $ext_data['coupon_source'],
                    );
                }

                //优惠明细汇总
                $objectCouponData[] = array(
                    'order_bn' => $ext_data['order_bn'],
                    'num' => $oov['quantity'],
                    'material_name' => $oov['name'],
                    'material_bn' => $oov['bn'],
                    'oid' => $oov['oid'],
                    'create_time' => time(),
                    'shop_id' => $ext_data['shop_id'],
                    'shop_type' => $ext_data['shop_type'],
                    'source' => 'push',
                    'addon' => serialize($oiv['extend_item_list']),
                    'org_id' => $orgId,
                );
            }
        }

        # 商家改价 - 订单级别
        $order_adjust_amount = bcdiv($ext_data['price_info']['change_down_price'], 100, 2);
        if (floatval($order_adjust_amount) > 0 && $split_result['total_amount'] > 0) {
            # 分摊比例
            $split_objects = array_values($split_result['split_objects']);
            $split_rate = bcdiv($order_adjust_amount, $split_result['total_amount'], 6);
            $split_count = count($split_objects);
            $split_amount = $order_adjust_amount;
            # 按oid分摊
            foreach ($split_objects as $k => $object) {
                if ($k + 1 == $split_count) {
                    $adjust_amount = $split_amount;
                } else {
                    $adjust_amount = bcmul($object['divide_order_fee'], $split_rate, 2);
                    $split_amount = bcsub($split_amount, $adjust_amount, 2);
                }
                $coupon[] = array(
                    'oid' => $object['oid'],
                    'platform_type' => 'shop_discount',
                    'type' => 'adjust',
                    'sub_type' => 'order',
                    'coupon_name' => '商家订单改价',
                    'num' => $object['quantity'],
                    'material_bn' => $object['bn'],
                    'coupon_amount' => $adjust_amount,
                    'create_time' => time(),
                    'source' => $ext_data['coupon_source'],
                );
            }
        }

        # 查询优惠券详情
        $couponResult = [];
        $coupon_params = ['coupon_meta_id' => [], 'user_coupon_id' => []];
        # 筛选出来优惠券id和用户优惠券ID列表
        foreach ($coupon as $k => $v) {
            # 过滤掉平台优惠
            if ($v['coupon_type'] == 3) {
                continue;
            }
            if (!empty($v['coupon_meta_id'])) {
                $coupon_params['coupon_meta_id'][] = $v['coupon_meta_id'];
            } elseif (!empty($v['coupon_id']) && empty($v['coupon_meta_id'])) {
                $coupon_params['user_coupon_id'][] = $v['coupon_id'];
            }
        }

        # 获取查询的优惠券ID，需要去重
        if (!empty($coupon_params['coupon_meta_id'])) {
            $coupon_id = array_unique($coupon_params['coupon_meta_id']);
            foreach ($coupon_id as $item) {
                # 查询优惠券详情
                $params = [
                    'order_bn' => $ext_data['order_bn'],
                    'coupon_id' => $item,
                ];
                $result = kernel::single('erpapi_router_request')->set('shop', $ext_data['shop_id'])->coupon_couponsInfo($params);
                if ($result['rsp'] == 'fail') {
                    continue;
                }
                $couponResult[$item] = $result['data'];
            }

            foreach ($coupon as $key => $item) {
                if (empty($item['coupon_meta_id']) || empty($couponResult[$item['coupon_meta_id']])) {
                    continue;
                }

                $couponInfo = $couponResult[$item['coupon_meta_id']];
                # 设定优惠券类型和名称
                $coupon[$key]['coupon_type'] = $couponInfo['coupon_type'];
                $coupon[$key]['coupon_name'] = $couponInfo['coupon_name'];
                $coupon[$key]['sub_type'] = $couponInfo['promote_info']['promote_type']; // 推广类型
            }
        }

        # 获取用户优惠券ID
        if (!empty($coupon_params['user_coupon_id'])) {
            # 根据用户优惠券查询优惠券详情
            $params = [
                'order_bn' => $ext_data['order_bn'],
                'user_coupon_id' => array_unique($coupon_params['user_coupon_id']),
                'openid' => $ext_data['openid']
            ];
            $couponResult = kernel::single('erpapi_router_request')->set('shop', $ext_data['shop_id'])->coupon_couponDetailGet($params);
            # 合并优惠券
            if (!empty($couponResult)) {
                foreach ($coupon as $key => $item) {
                    if (empty($item['coupon_id']) || empty($couponResult[$item['coupon_id']])) {
                        continue;
                    }
                    $couponInfo = $couponResult[$item['coupon_id']];
                    # 设定优惠券类型和名称
                    $coupon[$key]['coupon_type'] = $couponInfo['coupon_type'];
                    $coupon[$key]['coupon_name'] = $couponInfo['coupon_name'];
                    $coupon[$key]['sub_type'] = $couponInfo['promote_info']['promote_type']; // 推广类型
                    $coupon[$key]['coupon_meta_id'] = empty($item['coupon_meta_id']) ? $couponInfo['coupon_id'] : $item['coupon_meta_id']; // 优惠券ID
                }
            }
        }

        $res = [];
        $res['coupon_data'] = $coupon;
        $res['objects_coupon_data'] = $objectCouponData;
        return $res;
    }

    /**
     * 小红书优惠
     * @param $data
     * @param $ext_data
     * @return void
     */
    public function _XhsCouponDataFormat($data, $ext_data)
    {
        $orderObjects = array_column($ext_data['order_objects'], null, 'oid');
        $coupon = [];
        $skuList = $data['items'] ?? [];

        # 支付优惠
        if (isset($data['pay_discount_details']) && floatval($data['pay_discount_details']['total_amount']) > 0) {
            # 支付优惠的金额
            $pay_discount_amount = $data['pay_discount_details']['total_amount'];
            # 支付优惠因为金额太小，因此放到订单明细中实付金额最大的sku上
            $maxObject = [];
            foreach ($orderObjects as $object) {
                if (floatval($object['divide_order_fee']) <= 0 || bccomp($object['divide_order_fee'], $pay_discount_amount, 2) < 0) {
                    continue;
                }
                # 取金额最大的
                if (!empty($maxObject) && bccomp($maxObject['divide_order_fee'], $object['divide_order_fee'], 2) >= 0) {
                    continue;
                }
                $maxObject = $object;
            }

            # 将支付优惠分摊到最大金额上
            if (!empty($maxObject)) {
                $coupon[] = array(
                    'oid' => $maxObject['oid'],
                    'platform_type' => 'platform_discount', // 平台承担优惠
                    'type' => 'pay_discount',
                    'coupon_name' => '支付优惠',
                    'material_bn' => $maxObject['bn'],
                    'coupon_type' => 3,
                    'num' => $maxObject['quantity'],
                    'coupon_amount' => $pay_discount_amount,
                    'create_time' => time(),
                    'source' => $ext_data['coupon_source'],
                );
            }

            # 扩展信息
            $extendData = [
                'pay_discount_details' => $data['pay_discount_details']
            ];
        }

        # 平台承担优惠
        if (isset($data['platform_discount_detail']) && floatval($data['platform_discount_detail']['total_amount']) > 0 && !empty($skuList)) {
            # 根据Oid分组sku明细
            foreach ($skuList as $item) {
                $oid = $item['oid'];
                $coupon[] = array(
                    'oid' => $oid,
                    'platform_type' => 'platform_discount', // 平台承担优惠
                    'type' => 'discount',
                    'coupon_name' => '平台承担优惠',
                    'material_bn' => $orderObjects[$oid]['bn'] ?? '',
                    'coupon_type' => 1,
                    'num' => $orderObjects[$oid]['quantity'] ?? 1,
                    'coupon_amount' => $item['platform_discount_detail']['total_amount'],
                    'create_time' => time(),
                    'source' => $ext_data['coupon_source'],
                );
            }
        }

        # 商家承担优惠
        if (isset($data['merchant_discount_detail']) && floatval($data['merchant_discount_detail']['total_amount']) > 0 && !empty($skuList)) {
            foreach ($skuList as $item) {
                $oid = $item['oid'];
                $coupon[] = array(
                    'oid' => $oid,
                    'platform_type' => 'shop_discount', // 商家承担优惠
                    'type' => 'discount',
                    'coupon_name' => '商家承担优惠',
                    'material_bn' => $orderObjects[$oid]['bn'] ?? '',
                    'coupon_type' => 0,
                    'num' => $orderObjects[$oid]['quantity'] ?? 1,
                    'coupon_amount' => $item['merchant_discount_detail']['total_amount'],
                    'create_time' => time(),
                    'source' => $ext_data['coupon_source'],
                );
            }
        }

        $res = [];
        $res['coupon_data'] = $coupon;
        $res['platform_discount_total'] = $data['platform_discount_detail']['total_amount'] ?? 0;
        $res['merchant_discount_total'] = $data['merchant_discount_detail']['total_amount'] ?? 0;
        $res['pay_discount_total'] = $data['pay_discount_details']['total_amount'] ?? 0;
        return $res;
    }

    public function _JdCouponDataFormat($data, $ext_data)
    {
        $orderObjects = array_column($ext_data['order_objects'], null, 'oid');
        $coupon = array();
        $objectCouponData = array();
        if ($data && is_array($data['skuList'])) {
            $skuList = $data['skuList'];
            $venderFee = 0;
            $count = count($skuList);
            $i = 1;
            //整单平台优惠
            $totalPlatformDiscounts = $data['totalPingTaiChengDanYouHuiQuan'] + $data['totalJingQuan'] + $data['totalDongQuan'] +
                $data['totalXianPinLeiJingQuan'] + $data['totalXianPinLeiDongQuan'] + $data['totalJingDou'] + $data['totalSuperRedEnvelope'];
            //整单平台支付优惠
            $totalPlatformPayDiscounts = $data['totalLiJinYouHui'] + $data['totalZhiFuYingXiaoYouHui'] + $data['totalJdZhiFuYouHui'];
            //整单平台付款总价
            $taotalPlatformTotalPrice = $totalPlatformDiscounts + $totalPlatformPayDiscounts;
            //根据店铺取运营组织
            $shopInfo = kernel::single('ome_shop')->getRowByShopId($ext_data['shop_id']);
            $orgId = $shopInfo['org_id'];
            $divideOrderFeeZero = [];

            //整单实付金额
            $totalDivideOrderFee = $data['totalShouldPay'] + $data['totalBalance'] - $data['totalLiJinYouHui'] - $data['totalZhiFuYingXiaoYouHui'] -
                $data['totalJdZhiFuYouHui'] - $data['totalVenderFee'] - $data['totalBaseFee'] - $data['totalRemoteFee'] -
                $data['totalTuiHuanHuoWuYou'] - $data['totalTaxFee'] - $data['totalLuoDiPeiService'] -
                $data['totalGlobalGeneralTax'] - $data['totalGlobalGeneralIncludeTax'];
            $divideOrderFeeAmount = $totalDivideOrderFee;

            foreach ($skuList as $key => $value) {
                //实付金额
                $divideOrderFee = $value['shouldPay'] + $value['balance'] - $value['liJinYouHui'] - $value['zhiFuYingXiaoYouHui'] -
                    $value['jdZhiFuYouHui'] - $value['venderFee'] - $value['baseFee'] - $value['remoteFee'] -
                    $value['tuiHuanHuoWuYou'] - $value['taxFee'] - $value['luoDiPeiService'] -
                    $value['globalGeneralTax'] - $value['globalGeneralIncludeTax'];

                if ($divideOrderFee < 0) {
                    $divideOrderFee = $value['shouldPay'] + $value['balance'] - $value['liJinYouHui'] - $value['zhiFuYingXiaoYouHui'] -
                        $value['jdZhiFuYouHui'] - $value['baseFee'] - $value['remoteFee'] -
                        $value['tuiHuanHuoWuYou'] - $value['taxFee'] - $value['luoDiPeiService'] -
                        $value['globalGeneralTax'] - $value['globalGeneralIncludeTax'];
                    $venderFee += $value['venderFee'] * $value['count'];
                }

                //优惠分摊
                $partMjzDiscount = $value['skuPrice'] - $value['baseDiscount'] + $value['tuiHuanHuoWuYou'] +
                    $value['taxFee'] + $value['luoDiPeiService'] + $value['globalGeneralTax'] + $value['globalGeneralIncludeTax'] -
                    $divideOrderFee;
                //商家优惠
                $merchantDiscounts = $value['manJian'] + $value['coupon'] - $value['jingQuan'] - $value['dongQuan'] -
                    $value['xianPinLeiJingQuan'] - $value['xianPinLeiDongQuan'] + $value['plus95'] - $value['pingTaiChengDanYouHuiQuan'];
                //平台优惠
                $platformDiscounts = $value['pingTaiChengDanYouHuiQuan'] + $value['jingQuan'] + $value['dongQuan'] +
                    $value['xianPinLeiJingQuan'] + $value['xianPinLeiDongQuan'] + $value['jingDou'] + $value['superRedEnvelope'];
                //平台支付优惠
                $platformPayDiscounts = $value['liJinYouHui'] + $value['zhiFuYingXiaoYouHui'] + $value['jdZhiFuYouHui'];
                //其他
                $otherPrice = $value['tuiHuanHuoWuYou'] + $value['taxFee'] + $value['luoDiPeiService'] + $value['globalGeneralTax'] + $value['globalGeneralIncludeTax'];
                //平台付款总价
                $platformTotalPrice = $platformDiscounts + $platformPayDiscounts;
                //实付为0 的oid
                if ($divideOrderFee <= 0) {
                    $divideOrderFeeZero[] = $value['skuCode'];
                }
                //明细实付
                $priceList[$value['skuCode'] . '-' . $value['count']][] = [
                    'divide_order_fee' => ($divideOrderFee > 0 ? $divideOrderFee : 0) * $value['count'] + ($platformTotalPrice > 0 ? $platformTotalPrice : 0) * $value['count'],
                    'part_mjz_discount' => ($partMjzDiscount > 0 ? $partMjzDiscount : 0) * $value['count'],
                    'count' => $value['count'],
                    'cost_freight' => $value['venderFee'] * $value['count'],
                ];

                //平台优惠类型
                $platformCouponField = [
                    'superRedEnvelope',
                    'jingQuan',
                    'dongQuan',
                    'jingDou',
                    'xianPinLeiJingQuan',
                    'xianPinLeiDongQuan',
                    'pingTaiChengDanYouHuiQuan',
                ];
                //平台支付优惠
                $platformPayCouponField = [
                    'liJinYouHui',
                    'zhiFuYingXiaoYouHui',
                    'jdZhiFuYouHui',
                    'jingXiangLiJin',
                    'globalGeneralTax',
                    'globalGeneralIncludeTax',
                ];
                //商家优惠类型
                $MerchantCouponField = [
                    'manJian',
                    'plus95'
                ];
                $typeName = [
                    'skuPrice' => '单品金额',
                    'baseDiscount' => '基础优惠',
                    'manJian' => '满减',
                    'venderFee' => '商家运费',
                    'baseFee' => '基础运费',
                    'remoteFee' => '偏远运费',
                    'coupon' => '优惠券',
                    'jingDou' => '京豆',
                    'balance' => '余额',
                    'plus95' => 'plus会员95折优惠',
                    'tuiHuanHuoWuYou' => '退换货无忧',
                    'taxFee' => '全球购税费',
                    'luoDiPeiService' => '落地配服务',
                    'shouldPay' => '应付金额',
                    'superRedEnvelope' => '超级红包',
                    'jingQuan' => '京券',
                    'dongQuan' => '东券',
                    'xianPinLeiJingQuan' => '限品类京券',
                    'xianPinLeiDongQuan' => '限品类东券',
                    'pingTaiChengDanYouHuiQuan' => '按比例平台承担优惠券',
                    'liJinYouHui' => '礼金优惠',
                    'zhiFuYingXiaoYouHui' => '支付营销优惠',
                    'jdZhiFuYouHui' => '京东支付优惠',
                    'jingXiangLiJin' => '京享礼金(首单礼金或重逢礼金)',
                    'globalGeneralTax' => '全球购一般贸易税',
                    'globalGeneralIncludeTax' => '全球购一般贸易税(包税)',
                ];
                //优惠明细
                foreach ($value as $k => $v) {
                    if ($v <= 0 || $k == 'count' || $k == 'skuName' || $k == 'skuCode' || is_array($v)) {
                        continue;
                    }
                    //优惠类型
                    $coupon_type = '0';
                    if (in_array($k, $platformCouponField)) {
                        $coupon_type = '1';
                    } elseif (in_array($k, $MerchantCouponField)) {
                        $coupon_type = '2';
                    } elseif (in_array($k, $platformPayCouponField)) {
                        $coupon_type = '3';
                    }
                    $coupon[] = array(
                        'num' => $value['count'],
                        'material_bn' => $orderObjects[$value['skuCode']]['bn'],
                        'oid' => $value['skuCode'],
                        'material_name' => $value['skuName'],
                        'type' => $k,
                        'type_name' => $typeName[$k],
                        'coupon_type' => $coupon_type,
                        'amount' => $v,
                        'total_amount' => $v * $value['count'],
                        'create_time' => kernel::single('ome_func')->date2time($ext_data['createtime']),
                        'pay_time' => kernel::single('ome_func')->date2time($ext_data['payment_detail']['pay_time']),
                        'shop_type' => $ext_data['shop_type'],
                        'source' => $ext_data['coupon_source'],
                    );
                }

                //扩展字段
                $extendField = [
                    'calcActuallyPay' => $divideOrderFee, //实付金额
                    'calcPartMjzDiscount' => $partMjzDiscount, //优惠分摊
                    'calcMerchantDiscounts' => $merchantDiscounts, //商家优惠
                    'calcPlatformDiscounts' => $platformDiscounts, //平台优惠
                    'calcPlatformPayDiscounts' => $platformPayDiscounts, //平台支付优惠
                    'calcOtherPrice' => $otherPrice, //其他
                    'calcPlatformTotalPrice' => $platformTotalPrice, //平台付款总价
                ];
                $extendFieldName = [
                    'calcActuallyPay' => '实付金额', //实付金额
                    'calcPartMjzDiscount' => '优惠分摊', //优惠分摊
                    'calcMerchantDiscounts' => '商家优惠', //商家优惠
                    'calcPlatformDiscounts' => '平台优惠', //平台优惠
                    'calcPlatformPayDiscounts' => '平台支付优惠', //平台支付优惠
                    'calcOtherPrice' => '其他', //其他
                    'calcPlatformTotalPrice' => '平台付款总价', //平台付款总价
                ];

                foreach ($extendField as $field => $calcPrice) {
                    if ($calcPrice <= 0) {
                        continue;
                    }
                    $total_amount = $calcPrice * $value['count'];
                    if ($count == $i) {
                        if ($field == 'calcPlatformDiscounts') {
                            $total_amount = $totalPlatformDiscounts;
                        } elseif ($field == 'calcPlatformPayDiscounts') {
                            $total_amount = $totalPlatformPayDiscounts;
                        } elseif ($field == 'calcPlatformTotalPrice') {
                            $total_amount = $taotalPlatformTotalPrice;
                        }
                    } else {
                        if ($field == 'calcPlatformDiscounts') {
                            $totalPlatformDiscounts = $totalPlatformDiscounts - $total_amount;
                        } elseif ($field == 'calcPlatformPayDiscounts') {
                            $totalPlatformPayDiscounts = $totalPlatformPayDiscounts - $total_amount;
                        } elseif ($field == 'calcPlatformTotalPrice') {
                            $taotalPlatformTotalPrice = $taotalPlatformTotalPrice - $total_amount;
                        }
                    }
                    $extendData = [
                        'num' => $value['count'],
                        'material_bn' => $orderObjects[$value['skuCode']]['bn'],
                        'oid' => $value['skuCode'],
                        'material_name' => $value['skuName'],
                        'type' => $field,
                        'type_name' => $extendFieldName[$field],
                        'coupon_type' => '0',
                        'amount' => $calcPrice > 0 ? $calcPrice : 0,
                        'total_amount' => $total_amount,
                        'create_time' => kernel::single('ome_func')->date2time($ext_data['createtime']),
                        'pay_time' => kernel::single('ome_func')->date2time($ext_data['payment_detail']['pay_time']),
                        'shop_type' => $ext_data['shop_type'],
                        'source' => $ext_data['coupon_source'],
                    ];
                    array_push($coupon, $extendData);
                }
                //优惠明细汇总
                $objectCouponData[] = array(
                    'order_bn' => $ext_data['order_bn'],
                    'num' => $value['count'],
                    'material_name' => $value['skuName'],
                    'material_bn' => $orderObjects[$value['skuCode']]['bn'],
                    'oid' => $value['skuCode'],
                    'create_time' => kernel::single('ome_func')->date2time($ext_data['createtime']),
                    'shop_id' => $ext_data['shop_id'],
                    'shop_type' => $ext_data['shop_type'],
                    'source' => $ext_data['coupon_source'],
                    'addon' => serialize(array_merge($extendField, $value)),
                    'org_id' => $orgId,
                );
                $i++;
            }
        }
        $res['coupon_data'] = $coupon;
        $res['divide_order_fee_mount'] = $divideOrderFeeAmount;
        $res['vender_fee_amount'] = $venderFee;
        $res['price_list'] = $priceList;
        $res['objects_coupon_data'] = $objectCouponData;
        $res['divide_order_fee_zero'] = $divideOrderFeeZero;
        return $res;
    }

    /**
     * 查询订单优惠数据format
     * @param $order_id
     * @param $list
     * @param string $index
     * @return array|mixed|null
     */
    public function getOrderCoupon($order_id, $list = array())
    {
        static $newData;

        if (isset($newData)) {
            if ($order_id == 0) {
                return $newData;
            }
            if (isset($newData[$order_id])) {
                return $newData[$order_id];
            }
        }
        $filter['order_id'] = array_column($list, 'order_id');

        if (empty($list)) {
            $filter['order_id'] = $order_id;
        }
        $orderCouponDetailList = app::get('ome')->model('order_coupon')->getList(
            'order_id,material_bn,type,num,amount,oid,source,total_amount,shop_type',
            $filter
        );

        $orderDetailList = app::get('ome')->model('orders')->getList('order_id,order_bn,shop_id', $filter);
        $orderDetail = array_column($orderDetailList, null, 'order_id');
        $shopId = array_unique(array_column($orderDetailList, 'shop_id'));
        $shopDetailList = app::get('ome')->model('shop')->getList('shop_id,name', array('shop_id' => $shopId));
        $shopDetail = array_column($shopDetailList, null, 'shop_id');

        foreach ($orderCouponDetailList as $key => $value) {
            $newData[$value['order_id']][$value['oid']]['order_id'] = $value['order_id'];
            $newData[$value['order_id']][$value['oid']]['material_bn'] = $value['material_bn'];
            $newData[$value['order_id']][$value['oid']][$value['type']] = $value['amount'];
            $newData[$value['order_id']][$value['oid']][$value['type'] . 'TotalAmount'] = $value['total_amount'];
            $newData[$value['order_id']][$value['oid']]['num'] = $value['num'];
            $newData[$value['order_id']][$value['oid']]['order_bn'] = $orderDetail[$value['order_id']]['order_bn'];
            $newData[$value['order_id']][$value['oid']]['shop_name'] = $shopDetail[$orderDetail[$value['order_id']]['shop_id']]['name'];
            $newData[$value['order_id']][$value['oid']]['source'] = $value['source'];
            $newData[$value['order_id']][$value['oid']]['shop_type'] = $value['shop_type'];
        }
        if ($order_id == 0) {
            return $newData;
        }

        return $newData[$order_id];
    }

    public function getOrderItemCouponDetail($order_id, $list = array())
    {
        if (!$list) {
            $list = [['order_id' => $order_id]];
        }
        $orderCouponList = $this->getOrderCoupon($order_id, $list);
        if (!$orderCouponList) {
            return array();
        }
        $ordersModel = app::get('ome')->model('orders');
        $order = $ordersModel->dump(
            $order_id,
            '*',
            array('order_objects' => array('*', array('order_items' => array('*'))))
        );

        $objects = $order['order_objects'];
        if (empty($order['order_objects'])) {
            return array();
        }

        // todo.XueDing:共用占比方法app/ome/lib/order.php->calculate_part_porth
        //查询捆绑占比
        $smIds = array_column($objects, 'goods_id');
        $smBc = app::get('material')->model('sales_basic_material')->getList(
            'sm_id, bm_id, rate',
            array('sm_id' => $smIds)
        );
        $smBmRate = array();
        foreach ($smBc as $v) {
            $smBmRate[$v['sm_id']][$v['bm_id']] = $v['rate'];
        }
        $newdata = array();
        foreach ($objects as $key => $value) {
            if ($value['delete'] == 'true' || empty($value['order_items'])) {
                continue;
            }
            $countItem = count($value['order_items']);
            $i = 1;
            $couponRow = $orderCouponList[$value['oid']];
            $actuallyPay = $couponRow['calcActuallyPay'] * $couponRow['num'];
            $partMjzDiscount = $couponRow['calcPartMjzDiscount'] * $couponRow['num'];
            $merchantDiscounts = $couponRow['calcMerchantDiscounts'] * $couponRow['num'];
            $platformDiscounts = $couponRow['calcPlatformDiscounts'] * $couponRow['num'];
            $platformPayDiscounts = $couponRow['calcPlatformPayDiscounts'] * $couponRow['num'];
            $otherPrice = $couponRow['calcOtherPrice'] * $couponRow['num'];
            $platformTotalPrice = $couponRow['calcPlatformTotalPrice'] * $couponRow['num'];
            $skuNum = $couponRow['num'];
            $actuallyAmount = $merchantDiscountsAmount = $platformDiscountsAmount = $platformPayDiscountsAmounts = $otherPriceAmounts = $platformTotalPriceAmounts = $partMjzDiscountAmount = 0;
            foreach ($value['order_items'] as $k => $item) {
                if ($item['item_type'] == 'pkg') {
                    $rate = isset($smBmRate[$value['goods_id']][$item['product_id']]) ? $smBmRate[$value['goods_id']][$item['product_id']] : 0;
                    if ($i < $countItem) {
                        $itemActuallyPay = bcmul($rate, $actuallyPay, 2);
                        $actuallyAmount = bcadd($actuallyAmount, $itemActuallyPay, 2);

                        $itemCalcPartMjzDiscount = bcmul($rate, $partMjzDiscount, 2);
                        $partMjzDiscountAmount = bcadd($partMjzDiscountAmount, $itemCalcPartMjzDiscount, 2);

                        $itemMerchantDiscounts = bcmul($rate, $merchantDiscounts, 2);
                        $merchantDiscountsAmount = bcadd($merchantDiscountsAmount, $itemMerchantDiscounts, 2);

                        $itemPlatformDiscounts = bcmul($rate, $platformDiscounts, 2);
                        $platformDiscountsAmount = bcadd($platformDiscountsAmount, $itemPlatformDiscounts, 2);

                        $itemPlatformPayDiscounts = bcmul($rate, $platformPayDiscounts, 2);
                        $platformPayDiscountsAmounts = bcadd($platformPayDiscountsAmounts, $itemPlatformPayDiscounts, 2);

                        $itemOtherPrice = bcmul($rate, $otherPrice, 2);
                        $otherPriceAmounts = bcadd($otherPriceAmounts, $itemOtherPrice, 2);

                        $itemPlatformTotalPrice = bcmul($rate, $platformTotalPrice, 2);
                        $platformTotalPriceAmounts = bcadd($platformTotalPriceAmounts, $itemPlatformTotalPrice, 2);
                        $i++;
                    } else {
                        $itemActuallyPay = bcsub($actuallyPay, $actuallyAmount, 2);
                        $itemCalcPartMjzDiscount = bcsub($partMjzDiscount, $partMjzDiscountAmount, 2);
                        $itemMerchantDiscounts = bcsub($merchantDiscounts, $merchantDiscountsAmount, 2);
                        $itemPlatformDiscounts = bcsub($platformDiscounts, $platformDiscountsAmount, 2);
                        $itemPlatformPayDiscounts = bcsub($platformPayDiscounts, $platformPayDiscountsAmounts, 2);
                        $itemOtherPrice = bcsub($otherPrice, $otherPriceAmounts, 2);
                        $itemPlatformTotalPrice = bcsub($platformTotalPrice, $platformTotalPriceAmounts, 2);
                    }
                    $item['calcActuallyPay'] = $itemActuallyPay > 0 ? bcdiv($itemActuallyPay, $skuNum, 2) : 0;
                    $item['calcPartMjzDiscount'] = $itemCalcPartMjzDiscount > 0 ? bcdiv($itemCalcPartMjzDiscount, $skuNum, 2) : 0;
                    $item['calcMerchantDiscounts'] = $itemMerchantDiscounts > 0 ? bcdiv($itemMerchantDiscounts, $skuNum, 2) : 0;
                    $item['calcPlatformDiscounts'] = $itemPlatformDiscounts > 0 ? bcdiv($itemPlatformDiscounts, $skuNum, 2) : 0;
                    $item['calcPlatformPayDiscounts'] = $itemPlatformPayDiscounts > 0 ? bcdiv($itemPlatformPayDiscounts, $skuNum, 2) : 0;
                    $item['calcOtherPrice'] = $itemOtherPrice > 0 ? bcdiv($itemOtherPrice, $skuNum, 2) : 0;
                    $item['calcPlatformTotalPrice'] = $itemPlatformTotalPrice > 0 ? bcdiv($itemPlatformTotalPrice, $skuNum, 2) : 0;
                    $item['platform'] = '京东';
                    $item['source'] = $couponRow['source'];
                    $newdata[$item['bn']] = $item;
                } else {
                    if ($item['item_type'] == 'product') {
                        $item['calcActuallyPay'] = $actuallyPay > 0 ? bcdiv($actuallyPay, $skuNum, 2) : 0; //实付金额
                        $item['calcPartMjzDiscount'] = $partMjzDiscount > 0 ? bcdiv($partMjzDiscount, $skuNum, 2) : 0; //优惠分摊
                        $item['calcMerchantDiscounts'] = $merchantDiscounts > 0 ? bcdiv($merchantDiscounts, $skuNum, 2) : 0; //商家优惠
                        $item['calcPlatformDiscounts'] = $platformDiscounts > 0 ? bcdiv($platformDiscounts, $skuNum, 2) : 0; //平台优惠
                        $item['calcPlatformPayDiscounts'] = $platformPayDiscounts > 0 ? bcdiv($platformPayDiscounts, $skuNum, 2) : 0; //平台支付优惠
                        $item['calcOtherPrice'] = $otherPrice > 0 ? bcdiv($otherPrice, $skuNum, 2) : 0; //其他
                        $item['calcPlatformTotalPrice'] = $platformTotalPrice > 0 ? bcdiv($platformTotalPrice, $skuNum, 2) : 0; //平台付款总价
                        $item['platform'] = '京东'; //支付平台方
                        $item['source'] = $couponRow['source'];
                        $item['calcPlatformDiscountsTotalAmount'] = $couponRow['calcPlatformDiscountsTotalAmount'];
                        $item['calcPlatformPayDiscountsTotalAmount'] = $couponRow['calcPlatformPayDiscountsTotalAmount'];
                        $item['calcPlatformTotalPriceTotalAmount'] = $couponRow['calcPlatformTotalPriceTotalAmount'];
                        $newdata[$item['bn']] = $item;
                    }
                }
            }
        }
        return $newdata;
    }
}
