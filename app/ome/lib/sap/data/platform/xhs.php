<?php

class ome_sap_data_platform_xhs extends ome_sap_data_abstract
{
    /**
     * 平台活动类型映射
     * @var string[]
     */
    protected $discount_type_mapping = [
        'shop_discount' => '商家承担优惠',
        'platform_discount' => '平台承担优惠',
        'pay_discount' => '立减活动',
    ];

    protected $coupon_field_name = 'coupon_id';

    /**
     * 获取oid
     * @param $order_id
     * @param $oid
     * @return mixed
     */
    public function getOid($order_id, $oid)
    {
        return $oid;
    }

    /**
     * 获取订单的支付信息
     * @param $order_id
     * @return array
     */
    public function get_payments($params)
    {
        if (empty($params)) {
            return false;
        }

        $paymentsMdl = app::get('ome')->model('payments');
        $result = ['payment' => [], 'platform' => []];

        # 设定支付明细（不含运费）
        $realPaymentList = $this->getRealPaymentsList($params);
        if (!empty($realPaymentList['payment'])) {
            # 获取订单支付方式
            $paymentList = $paymentsMdl->getList('pay_bn,paymethod,money', array('order_id' => $params['order_id']));
            # 判断是否存在多种支付方式
            if (!empty($paymentList) && count($paymentList) > 1) {
                # 多种支付方式，需要按多种支付方式分摊到商品明细上
                $total_amount = array_sum(array_column($paymentList, 'money'));
                # 按sku明细分摊
                foreach ($realPaymentList['payment'] as $item) {
                    # 分摊金额
                    $split_amount = $item['money'];
                    $split_count = count($paymentList);
                    foreach ($paymentList as $k => $row) {
                        if ($k + 1 == $split_count) {
                            # 加入支付明细
                            $item['money'] = $split_amount;
                            $item['payment'] = $row['pay_bn'];      // 支付编码
                            $item['paymethod'] = $row['paymethod']; // 支付名称
                            $paymentItems[] = $item;
                        } else {
                            # 支付方式金额占比
                            $payment_rate = bcdiv($row['money'], $total_amount, 6);
                            # 重新计算分摊金额
                            $payment_amount = bcmul($item['money'], $payment_rate, 2);
                            # 加入支付明细
                            $item['money'] = $payment_amount;
                            $item['payment'] = $row['pay_bn'];      // 支付编码
                            $item['paymethod'] = $row['paymethod']; // 支付名称
                            $paymentItems[] = $item;
                            # 重新计算剩余分摊金额
                            $split_amount = bcsub($split_amount, $payment_amount, 2);
                        }
                    }
                }

                # 合并支付明细
                if (!empty($paymentItems)) {
                    $realPaymentList['payment'] = $paymentItems;
                }
            } else {
                # 只有一种支付方式，直接使用支付单上面的支付方式和编码
                foreach ($realPaymentList['payment'] as $k => $item) {
                    $realPaymentList['payment'][$k]['payment'] = $paymentList[0]['pay_bn'];      // 支付编码
                    // 支付名称
                    $pay_name = $paymentList[0]['paymethod'] ?? $params['payinfo']['pay_name'];
                    // 没有支付名称，设置为默认支付方式
                    if (empty($pay_name)) {
                        $pay_name = '未知';
                    }
                    $realPaymentList['payment'][$k]['paymethod'] = $pay_name;
                }
            }
            $result['payment'] = $realPaymentList['payment'];
        }

        # 合并支付优惠
        if (!empty($realPaymentList['other'])) {
            $result['payment'] = array_merge($result['payment'], $realPaymentList['other']);
        }

        # 平台优惠券、活动相关
        $plateformList = $this->getPlateformPromotion($params['order_id']);
        if (!empty($plateformList)) {
            foreach ($plateformList as $item) {
                $plateform = [
                    'oid' => $item['oid'],
                    'type' => 'platform',
                    'money' => floatval($item['money']),
                    'paymethod' => $item['paymethod'],
                    'sub_type' => $item['sub_type'],
                    'coupon_type' => $item['coupon_type'],
                    'category' => $item['category'],
                ];
                # 券号
                if (!empty($item['coupon_code'])) {
                    $plateform['coupon_code'] = $item['coupon_code'];
                }
                $result['platform'][] = $plateform;
            }
        }

        # 合并数据
        $result = array_merge($result['payment'], $result['platform']);
        return $result;
    }

    /**
     * 获取实际支付和支付优惠信息
     * @param $couponList
     * @param $params
     * @return array
     */
    private function getRealPaymentsList($params)
    {
        if (empty($params['order_objects'])) {
            return false;
        }

        $result = ['payment' => [], 'other' => []];
        $couponMdl = app::get('ome')->model('order_coupon_xhs');

        # 获取sku实付金额
        foreach ($params['order_objects'] as $object) {
            $payment = [
                'type' => 'payment',
                'money' => $object['divide_order_fee'],
                'paymethod' => '买家支付金额',
                'sub_type' => 'payment',
                'coupon_type' => 'payment',
                'oid' => $this->getOid($object['order_id'], $object['oid'])
            ];
            $result['payment'][] = $payment;
        }

        # 获取支付优惠
        $couponList = $couponMdl->getList('oid,coupon_amount', array('order_id' => $params['order_id'], 'platform_type' => 'pay_discount'));
        if (!empty($couponList)) {
            foreach ($couponList as $item) {
                $result['other'][] = [
                    'type' => 'payment',
                    'money' => $item['coupon_amount'],
                    'paymethod' => '支付优惠',
                    'sub_type' => 'butie',
                    'coupon_type' => 'butie',
                    'oid' => $this->getOid($params['order_id'], $item['oid'])
                ];
            }
        }

        return $result;
    }

    /**
     * 平台活动相关
     * @param $order_id
     * @return mixed
     */
    protected function getPlateformPromotion($order_id)
    {
        if (empty($order_id)) {
            return false;
        }

        $couponMdl = app::get('ome')->model('order_coupon_xhs');
        # 获取所有优惠列表
        $filter = [
            'order_id' => $order_id,
            'type' => ['discount', 'coupon']
        ];
        $couponList = $couponMdl->getList('*', $filter);
        if (empty($couponList)) {
            return false;
        }

        $result = array();
        foreach ($couponList as $coupon) {
            # 不传递esb相关配置
            if (!empty($this->notPushCouponTypeList) && in_array($coupon['type'], $this->notPushCouponTypeList)) {
                continue;
            }

            $data = [
                'oid' => $this->getOid($order_id, $coupon['oid']),
                'category' => $coupon['platform_type'],  // 所属分类
                'money' => $coupon['coupon_amount'],
                'sub_type' => $coupon['type'],
                'coupon_type' => $coupon['coupon_type'],
                'paymethod' => $coupon['coupon_name'],
            ];
            # 优惠券
            if ($coupon['type'] == 'coupon' && !empty($coupon[$this->coupon_field_name])) {
                $data['coupon_code'] = $coupon[$this->coupon_field_name];
            }
            $result[] = $data;
        }
        return $result;
    }

    /**
     * 优惠券列表
     * @param $order_id
     * @param $oid
     * @return array
     */
    public function get_coupons($order_id, $oid = [])
    {
        return [];
    }
}
