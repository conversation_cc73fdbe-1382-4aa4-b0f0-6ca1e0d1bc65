<div class="tableform">
<div class="division">
<form method="post" action="index.php?<{$env.server.QUERY_STRING}>" id="terminal">
<input type="hidden" name="wms_id" value="<{$wms_id}>">
      <table width="100%" cellspacing="0" cellpadding="0" border="0" >
        <tbody>
           <tr>
               <th>是否批量发货：</th>
               <td>
                   <{input type="radio" name="batch_delivery" value=$batch_delivery options=array("0"=>"否","1"=>"是") separator="&nbsp;"}>
               </td>
           </tr>
           <tr>
             <th>是否批量退货：</th>
             <td>
              <{input type="radio" name="batch_return" value=$batch_return options=array("0"=>"否","1"=>"是") separator="&nbsp;"}>
             </td>
           </tr>
           <tr>
               <th>是否库存查询：</th>
               <td>
                   <{input type="radio" name="batch_stock" value=$batch_stock options=array("0"=>"否","1"=>"是") separator="&nbsp;"}>
               </td>
           </tr>
           <tr>
            <th>是否要增加冻结库存：</th>
            <td>
                <{input type="radio" name="add_branch_freeze" value=$add_branch_freeze options=array("0"=>"否","1"=>"是") separator="&nbsp;"}>
            </td>
          </tr>
        </tbody>
      </table>
<div class="table-action">

<{button class="btn-primary" type="submit" id="saveterminal" name="submit" label="保存"}>

</div>
</form>
</div>
</div>

<script>
$('terminal').store('target',{
    onRequest:function(){
    },
    onComplete:function(jsontext){
      finderGroup['<{$env.get.finder_id}>'].refresh.delay(400,finderGroup['<{$env.get.finder_id}>']);
    }
});
</script>
