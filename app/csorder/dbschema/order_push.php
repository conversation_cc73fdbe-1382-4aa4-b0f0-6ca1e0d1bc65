<?php
$db['order_push'] = array(
    'columns' => array(
        // 主键
        'push_id' => array(
            'type' => 'int unsigned',
            'required' => true,
            'pkey' => true,
            'editable' => false,
            'extra' => 'auto_increment',
            'comment' => '推送记录ID'
        ),

        // 订单信息
        'order_id' => array(
            'type' => 'table:orders@ome',
            'required' => true,
            'label' => '订单ID',
            'comment' => '关联ome_orders.order_id'
        ),
        'order_bn' => array(
            'type' => 'varchar(32)',
            'required' => true,
            'label' => '订单号',
            'is_title' => true,
            'width' => 180,
            'searchtype' => 'nequal',
            'editable' => false,
            'filtertype' => 'normal',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'member_id' => array(
            'type' => 'varchar(64)',
            'label' => 'uid',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
            'comment' => '来自子订单表的会员卡号'
        ),
        'shop_id' => array(
            'type' => 'table:shop@ome',
            'label' => '店铺ID',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
            'comment' => '关联ome_shop.shop_id'
        ),
        // 时间信息
        'order_time' => array(
            'type' => 'time',
            'label' => '下单时间',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'pay_time' => array(
            'type' => 'time',
            'label' => '支付时间',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'refund_time' => array(
            'type' => 'time',
            'label' => '退款时间',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        // 订单数据
        'item_num' => array(
            'type' => 'int unsigned',
            'label' => '总件数',
            'width' => 80,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'total_amount' => array(
            'type' => 'decimal(10,2)',
            'label' => '总金额',
            'width' => 100,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'order_status' => array(
            'type' => array(
                'pay' => '已支付',
                'ship' => '已发货',
                'refund' => '退款',
                'cancel' => '取消',
            ),
            'label' => '订单状态',
            'width' => 100,
            'editable' => false,
            'filtertype' => 'yes',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'status'=>array(
            'type' => array(
                '2' => '正常订单',
                '3' => '撤销订单',
                '4' => '退款订单',
            ),
            'label' => '推送订单状态',
            'width' => 100,
            'editable' => false,
            'filtertype' => 'yes',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => true,
            'default' => '2',
        ),
        // 退款信息
        'refund_no' => array(
            'type' => 'varchar(64)',
            'label' => '退款单号',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'refund_amount' => array(
            'type' => 'decimal(10,2)',
            'label' => '退款金额',
            'width' => 100,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),

        // 推送状态
        'push_status' => array(
            'type' => array(
                'pending' => '待推送',
                'success' => '推送成功',
                'failed' => '推送失败',
            ),
            'default' => 'pending',
            'label' => '推送状态',
            'width' => 100,
            'editable' => false,
            'filtertype' => 'yes',
            'filterdefault' => true,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'push_time' => array(
            'type' => 'time',
            'label' => '推送时间',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'push_message' => array(
            'type' => 'text',
            'label' => '推送消息',
            'width' => 200,
            'editable' => false,
            'in_list' => false,
            'default_in_list' => true,
        ),

        // 框架字段
        'createtime' => array(
            'type' => 'time',
            'label' => '创建时间',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
        'last_modified' => array(
            'type' => 'time',
            'label' => '最后修改时间',
            'width' => 150,
            'editable' => false,
            'in_list' => true,
            'default_in_list' => true,
        ),
    ),

    'index' => array(
        'ind_order_id' => array('columns' => array('order_id')),
        'ind_order_bn' => array('columns' => array('order_bn')),
        'ind_shop_id' => array('columns' => array('shop_id')),
        'ind_push_status' => array('columns' => array('push_status')),
        'ind_createtime' => array('columns' => array('createtime')),
    ),

    'comment' => '客服工单推送记录表',
);