<?php

/**
 * Class csorder_ctl_admin_order_push
 */
class csorder_ctl_admin_order_push extends desktop_controller{
    var $name = "客服订单推送列表";
    var $workground = "invoice_center";

    function index(){

        $actions[] =  array(
            'label'=>'推送',
            'submit'=>'index.php?app=csorder&ctl=admin_order_push&act=push_order',
            'target'=>'dialog::{width:600,height:300,title:\'推送结果\'}'
        );

        $this->finder('csorder_mdl_order_push',array(
            'title' => '客服订单推送列表',
            'base_filter' => [],
            'actions' => $actions,
            'use_buildin_new_dialog' => false,
            'use_buildin_set_tag'=>false,
            'use_buildin_recycle'=>false,
            'use_buildin_export'=>false,
            'use_buildin_import'=>false,
            'use_buildin_filter'=>true,
        ));
    }

    public function push_order(){
        $order_push_mdl = app::get('csorder')->model('order_push');
        $push_ids = array('push_id' => $_POST['push_id']);
        $push_list = $order_push_mdl->getlist('*', $push_ids);

        if(!$push_list || count($push_list)<=0) {
            $this->pagedata['push_list'] = ['没有找到对应的推送数据'];
            $this->display('order/push.html');
        }

        //推送
        $res_push_list = [];
        $lib_push = new csorder_push();
        foreach ($push_list as $push_info){
            if($push_info['push_status'] == 'success'){
                continue;
            }

            $push_res = $lib_push->push($push_info);
            if($push_res['status'] != 'succ'){
                $res_push_list[] = $push_res['msg'];
            }
        }

        $this->pagedata['push_list'] = $res_push_list;
        $this->display('order/push.html');
    }
}