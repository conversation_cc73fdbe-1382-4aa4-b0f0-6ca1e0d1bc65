<?php

/**
 * 推送逻辑类
 * Class csorder_push
 */
class csorder_push
{
    /**
     * 推送
     * @param $params
     * @return bool
     */
    public function push($params)
    {
        if(!$params){
            return ['status'=>'fail','msg'=>'没有数据'];
        }

        if(!is_array($params)){
            $params = [$params];
        }

        $csorder_http = new csorder_http();

        $appKey = QI_YU_APPKEY; //准备app key
        $appSecret = QI_YU_APPSECRET; //准备app secret
        $time = time(); //准备时间缀

        // 准备需要请求json
        $data_string = json_encode($params); // 将需要请求的对象字符串化
        $data_string_length = strlen($data_string);//计算需要请求对象的长度
        $md5 = md5($data_string); // md5 加密一下
        $nonce = strtolower($md5); // md5 字母都小写
        $checkNum = sha1($appSecret.$nonce.$time); // 拼接起来Sha1加密

        $url = "https://qiyukf.com/openapi/worksheet/list?appKey=".$appKey."&time=".$time."&checksum=".$checkNum;

        $res = $csorder_http->curl($url, ['data_string'=>$data_string,'data_string_length'=>$data_string_length]);
        $pkey = date('Y-m-d H:i');
        $title = '推送客服工单';

        $params['url'] = $url;
        if ($res && $res['code'] == '200') {
            $csorder_http->write_log($title, $params['order_bn'], 'success', $params, $params, $res);
            return ['status' => 'succ', 'msg' => $res['message']];
        }

        $csorder_http->write_log($title, $pkey, 'fail', $params, $params, $res);
        return ['status' => 'fail', 'msg' => $params['order_bn'] . '推送失败:'.$res['message']];
    }

    private function base_check($order_info){
        if (!$order_info) {
            return ['status' => 'fail', 'msg' => '没有找到当前订单！'];
        }
        if ($order_info['shop_type'] != XCXSHOPTYPE) {
            return ['status' => 'fail', 'msg' => '只处理商城的订单！'];
        }
        return ['status' => 'succ', 'msg' => ''];
    }

    /**
     * 支付添加(订单支付了就添加)
     * @param $order_id
     * @return string[]
     */
    public function pay_add($order_id)
    {
        $order_obj = app::get('ome')->model('orders');
        $push_obj = app::get('csorder')->model('order_push');

        $old_push_info = $push_obj->dump(['order_id' => $order_id, 'order_status' => 'pay']);
        if ($old_push_info) {
            return ['status' => 'fail', 'msg' => '当前支付类型数据已经存在'];
        }

        //基础的一些检测
        $order_info = $order_obj->dump(['order_id' => $order_id]);
        $check_res = $this->base_check($order_info);
        if ($check_res['status'] != 'succ') {
            return $check_res;
        }

        if($order_info['pay_status']!='1') {
            return ['status' => 'fail', 'msg' => '只处理已支付订单！'];
        }

        //计算商品数量
        $item_num = app::get('ome')->model('order_items')->count(['order_id' => $order_id]);

        $add_data = [
            'order_id' => $order_id,
            'order_bn' => $order_info['order_bn'],
            'member_id' => $order_info['card_number'],
            'shop_id' => $order_info['shop_id'],
            'order_time' => $order_info['createtime'],
            'pay_time' => $order_info['paytime'],
            'item_num' => $item_num,
            'total_amount' => $order_info['total_amount'],
            'order_status' => 'pay',
            'push_status' => 'pending',
            'createtime' => time(),
            'status'=>'2'
        ];

        $res = $push_obj->insert($add_data);
        if ($res && $add_data['push_id']) {

            $push_params = array(
                'data' => array(
                    'task_type' => 'csorderpushtask',
                    'push_id' => $add_data['push_id'],
                ),
                'url' => kernel::openapi_url('openapi.autotask', 'service'),
            );
            kernel::single('taskmgr_interface_connecter')->push($push_params);

            return ['status' => 'succ', 'msg' => '添加成功支付客服工单成功！'];
        } else {
            return ['status' => 'fail', 'msg' => '添加成功支付客服工单失败！'];
        }
    }

    /**
     * 部分退款，未发货退款
     * @param $refund_id
     * @return string[]
     */
    public function refund_add($refund_id){
        $order_obj = app::get('ome')->model('orders');
        $push_obj = app::get('csorder')->model('order_push');
        $refunds_obj = app::get('ome')->model('refunds');

        $refund_info = $refunds_obj->dump(['refund_id'=>$refund_id]);
        if(!$refund_info) {
            return ['status' => 'fail', 'msg' => '没有找到对应的退款单'];
        }
        $order_id = $refund_info['order_id'];
        $refund_bn = $refund_info['refund_bn'];

        //订单信息检测
        $order_info = $order_obj->dump(['order_id'=>$order_id]);
        $check_res = $this->base_check($order_info);
        if ($check_res['status'] != 'succ') {
            return $check_res;
        }

        $pay_push_info = $push_obj->dump(['order_id' => $order_id, 'order_status' => 'pay']);
        if (!$pay_push_info) {
            return ['status' => 'fail', 'msg' => '没有找到支付订单的信息'];
        }

        $old_push_info = $push_obj->dump(['refund_no' => $refund_bn, 'order_status' => 'refund']);
        if ($old_push_info) {
            return ['status' => 'fail', 'msg' => '当前退款类型数据已经存在'];
        }

        $shop_id = $order_info['shop_id'];
        // 退款申请单
        $refund_apply_mdl = app::get('ome')->model('refund_apply');
        $refund_apply = $refund_apply_mdl->dump(array('refund_apply_bn'=>$refund_bn,'shop_id'=>$shop_id),'*');

        if(!$refund_apply){
            $return_mdl = app::get("ome")->model("return_product");
            $return_info = $return_mdl->dump(array("return_bn" => $refund_bn), "return_id");
            if($return_info && $return_info['return_id']){
                $refund_apply = $refund_apply_mdl->dump(array('return_id'=>$return_info['return_id'],'shop_id'=>$shop_id),'*');
            }
        }

        //退货商品数量
        $item_num = 0;
        if($refund_apply) {
            $product_data = $refund_apply['product_data'];
            if ($product_data) {
                $product_data = unserialize($product_data);
                if ($product_data) {
                    if (!isset($product_data[0])) {
                        $item_num = 1;
                    } else {
                        $item_num = count($product_data);
                    }
                }
            }
        }

        $add_data = [
            'order_id' => $order_id,
            'order_bn' => $order_info['order_bn'],
            'member_id' => $order_info['card_number'],
            'shop_id' => $order_info['shop_id'],
            'order_time' => $order_info['createtime'],
            'pay_time' => $order_info['paytime'],
            'item_num' => $item_num,
            'total_amount' => $refund_info['money'],
            'order_status' => 'pay',
            'push_status' => 'pending',
            'createtime' => time(),
            'status'=>'4',
            'refund_no'=>$refund_bn,
            'refund_amount'=>$refund_info['money'],
            'refund_time'=>$refund_info['']
        ];

        $res = $push_obj->insert($add_data);
        if ($res && $add_data['push_id']) {

            $push_params = array(
                'data' => array(
                    'task_type' => 'csorderpushtask',
                    'push_id' => $add_data['push_id'],
                ),
                'url' => kernel::openapi_url('openapi.autotask', 'service'),
            );
            kernel::single('taskmgr_interface_connecter')->push($push_params);

            return ['status' => 'succ', 'msg' => '添加退款客服工单成功！'];
        } else {
            return ['status' => 'fail', 'msg' => '添加退款客服工单失败！'];
        }
    }

    /**
     * 撤销
     * @param $order_id
     * @return string[]
     */
    public function cancel($order_id){
        $order_obj = app::get('ome')->model('orders');
        $push_obj = app::get('csorder')->model('order_push');

        $pay_push_info = $push_obj->dump(['order_id' => $order_id, 'order_status' => 'pay']);
        if (!$pay_push_info) {
            return ['status' => 'fail', 'msg' => '没有找到支付订单的信息'];
        }

        $old_push_info = $push_obj->dump(['order_id' => $order_id, 'order_status' => 'cancel']);
        if ($old_push_info) {
            return ['status' => 'fail', 'msg' => '当前取消类型数据已经存在'];
        }

        //基础的一些检测
        $order_info = $order_obj->dump(['order_id' => $order_id]);
        $check_res = $this->base_check($order_info);
        if ($check_res['status'] != 'succ') {
            return $check_res;
        }

        if($order_info['pay_status']!='1') {
            return ['status' => 'fail', 'msg' => '只处理已支付订单！'];
        }

        $add_data = [
            'order_id' => $order_id,
            'order_bn' => $order_info['order_bn'],
            'member_id' => $order_info['card_number'],
            'shop_id' => $order_info['shop_id'],
            'order_time' => $order_info['createtime'],
            'pay_time' => $order_info['paytime'],
            'item_num' => 0,
            'total_amount' => 0,
            'order_status' => 'cancel',
            'push_status' => 'pending',
            'createtime' => time(),
            'status'=>'3'
        ];

        $res = $push_obj->insert($add_data);
        if ($res && $add_data['push_id']) {

            $push_params = array(
                'data' => array(
                    'task_type' => 'csorderpushtask',
                    'push_id' => $add_data['push_id'],
                ),
                'url' => kernel::openapi_url('openapi.autotask', 'service'),
            );
            kernel::single('taskmgr_interface_connecter')->push($push_params);

            return ['status' => 'succ', 'msg' => '添加撤销订单客服工单成功！'];
        } else {
            return ['status' => 'fail', 'msg' => '添加撤销订单客服工单失败！'];
        }
    }
}