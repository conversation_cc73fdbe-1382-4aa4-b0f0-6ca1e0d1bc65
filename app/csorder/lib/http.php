<?php

/**
 * http 请求
 * Class csorder_http
 */
class csorder_http
{
    public $__start_time = 0;

    /**
     * http请求
     * @param $url
     * @param $params
     * @return bool|string
     */
    public function curl($url, $params){
        $this->__start_time = microtime(true);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json;charset=utf-8','Content-Length: ' . $params['data_string_length']));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS,$params['data_string']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $res  = curl_exec($ch);
        curl_close($ch);

        return json_decode($res, true);
    }


    /**
     * 日志
     *
     * @return void
     * <AUTHOR>
    public function write_log($title, $original_bn, $status = 'success', $params = array(), $convert_params = array(), $result = array())
    {
        // 写日志
        $apilogModel = app::get('ome')->model('api_log');
        $log_id      = $apilogModel->gen_id();

        if ($params['task'] && $result['rsp'] == 'succ') {
            $apilogModel->set_repeat($params['task'], $log_id);
        }
        $logParams = json_encode($params);
        $traParams = json_encode($convert_params);
        // $msg    = '接收参数：' . var_export($params, true) . '<hr/>转换后参数：' . var_export($convert_params, true) . '<hr/>返回结果：' . var_export($result, true);
        $logsdf = array(
            'log_id'        => $log_id,
            'task_name'     => $title,
            'status'        => $status,
            'worker'        => $params['act'],
            'params'        => strlen($logParams) < 256000 ? $logParams : '',
            'transfer'      => strlen($traParams) < 256000 ? $traParams : '',
            'response'      => json_encode($result),
            'msg'           => $result['msg'],
            'log_type'      => '',
            'api_type'      => 'request',
            'memo'          => '',
            'original_bn'   => $original_bn,
            'createtime'    => time(),
            'last_modified' => time(),
            'msg_id'        => (string)$params['msg_id'], //矩阵给的msg_id
            'spendtime'     => microtime(true) - $this->__start_time,
            'url'           => base_request::get_request_uri(),
        );

        $apilogModel->insert($logsdf);
    }
}