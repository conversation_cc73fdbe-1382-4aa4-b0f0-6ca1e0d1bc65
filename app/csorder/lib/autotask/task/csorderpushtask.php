<?php

/**
 * 推送工单任务
 * Class ome_autotask_task_csorderpushtask
 */
class csorder_autotask_task_csorderpushtask
{
    public function process($params, &$error_msg=''){

        $push_id = $params['push_id'];
        $push_obj = app::get('csorder')->model('order_push');
        $info = $push_obj->dump(['push_id'=>$push_id]);
        if(!$info){
            return true;
        }

        kernel::single('base_customlog')->saveLog('kafka', ['pullinfo'=>$info],'csorderpushtask');

        $lib_push = new csorder_push();

        $push_data = [
            'orderId'=>$info['order_bn'],
            'uid'=>$info['member_id'],
            'orderTime'=>$info['order_time']*1000,
            'payTime'=>$info['pay_time']*1000,
            'amount'=>$info['item_num'],
            'price'=>$info['total_amount'],
            'status'=>$info['status'],
            'refundNo'=>$info['refund_no']?$info['refund_no']:'',
            'refundTime'=>$info['refund_time']?$info['refund_time']*1000:0
        ];


        $res = $lib_push->push([$push_data]);
        kernel::single('base_customlog')->saveLog('kafka', ['pullres'=>$res],'csorderpushtask');

        $push_status = 'success';
        $opLogMdl = app::get('ome')->model('operation_log');
        if($res['status'] != 'succ'){
            $push_status = 'failed';
            $opLogMdl->write_log('csorder_push@csorder',$push_id,"推送失败:".$res['msg']);
        }
        $opLogMdl->write_log('csorder_push@csorder',$push_id,"推送成功");
        //更改推送状态
        $push_obj->update(['push_status'=>$push_status],['push_id'=>$push_id]);
        return true;
    }
}

