<?php

/**
 * @Author: <EMAIL>
 * @Date: 2023/4/20
 * @Describe: 发货单处理
 */
class erpapi_shop_matrix_wxshipin_request_delivery extends erpapi_shop_request_delivery
{
    /**
     * 发货请求参数
     *
     * @return void
     * <AUTHOR>
    protected function get_confirm_params($sdf)
    {
        $param = parent::get_confirm_params($sdf);

        $orderinfo = app::get('ome')->model('orders')->db_dump(['order_bn' => $param['tid']], 'order_id');
        if (is_array($sdf['goods']) && !empty($sdf['goods'])) {
            foreach ($sdf['goods'] as $key => $value) {
                if ((!$value['sku_id'] || !$value['product_id']) && $value['oid']) {
                    // 根据订单的oid获取
                    $orderObjects= app::get('ome')->model('order_objects')->db_dump(['order_id' => $orderinfo['order_id'], 'oid' => $value['oid']], 'shop_goods_id,obj_id');
                    $orderItems = app::get('ome')->model('order_items')->db_dump(['order_id' => $orderinfo['order_id'], 'obj_id' => $orderObjects['obj_id']], 'shop_product_id');
                    if ($orderObjects) {
                        $sdf['goods'][$key]['sku_id'] = $orderItems['shop_product_id'];
                        $sdf['goods'][$key]['product_id'] = $orderObjects['shop_goods_id'];
                    }
                }
            }
        }
        
        // 拆单子单回写
//        if ($sdf['is_split'] == 1) {
            $param['goods']        = json_encode($sdf['goods']);
//        }
        
        return $param;
    }

    /**
     * 修改物流公司信息
     *
     * @param array $params
     * @return array
     */
    public function logistics_edit($params)
    {
        $title = sprintf('修改物流信息[%s]-%s', $params['delivery_bn'], $params['order_bn']);
        $order_bn = $params['order_bn'];
        //params
        $requestParams = array(
            'tid' => $order_bn,
            'company_code' => $params['logi_code'],
            'logistics_no' => $params['logi_no'],
            'package_type'=>'normal',
            'goods'=>$params['goods'],
        );


        //request
        $result = $this->__caller->call(SHOP_LOGISTICS_CONSIGN_RESEND, $requestParams, null, $title, 10, $order_bn);
        if ($result['rsp'] != 'succ') {
            $result['error_msg'] = '修改物流信息';
            return $result;
        }

        return $result;
    }
}