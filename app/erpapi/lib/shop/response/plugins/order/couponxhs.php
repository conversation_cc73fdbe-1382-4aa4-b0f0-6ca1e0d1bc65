<?php

/**
 * 小红书平台原始优惠明细数据
 *
 * @access public
 * <AUTHOR>
 * @date  2025-07-30
 */
class erpapi_shop_response_plugins_order_couponxhs extends erpapi_shop_response_plugins_order_abstract
{
    public function convert(erpapi_shop_response_abstract $platform)
    {
        $coupon = array(
           'promotion_detail' => $platform->_ordersdf['coupon_data'] ?? [],
        );
        return $coupon;
    }

    /**
     * 订单完成后处理
     *
     * @return void
     * <AUTHOR>
    public function postCreate($order_id, $coupon_data)
    {
        //记录优惠明细
        $promotion_detail = $coupon_data['promotion_detail'];
        if (!empty($promotion_detail)) {
            $couponObj = app::get('ome')->model('order_coupon_xhs');
            foreach ($promotion_detail as $key => $value) {
                $promotion_detail[$key]['order_id'] = $order_id;
                $couponObj->save($promotion_detail[$key]);
            }
        }
    }

    public function postUpdate($order_id, $coupon_data)
    {
        //记录优惠明细
        $promotion_detail = $coupon_data['promotion_detail'];
        $couponObj = app::get('ome')->model('order_coupon_xhs');
        foreach ($promotion_detail as $key => $value) {
            $promotion_detail[$key]['order_id'] = $order_id;
        }
        if (!$couponObj->db_dump(array('order_id' => $order_id))) {
            $sql = ome_func::get_insert_sql($couponObj, $promotion_detail);
            kernel::database()->exec($sql);
        }
    }
}