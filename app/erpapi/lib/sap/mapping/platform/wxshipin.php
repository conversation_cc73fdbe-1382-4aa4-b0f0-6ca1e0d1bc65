<?php

class erpapi_sap_mapping_platform_wxshipin extends erpapi_sap_mapping_abstract
{
    /**
     * appid映射关系
     * @var array
     */
    protected $appId = 'WechatChannel';

    /**
     * orderSource映射关系
     * @var string
     */
    protected $order_source = '2';

    /**
     * 金蝶支付方式映射关系，格式：支付方式 => [DP支付名称,金蝶支付编码]
     * @var string[]
     */
    protected $payment_cfg = [
        '微信' => ['TCMP', 'TCMP'],
        '先用后付' => ['TCMP', 'TCMP'],
        '极速付' => ['TCMP', 'TCMP'],   // 等同于先用后付，矩阵写死的
        //        '抽奖商品0元订单' => '',
        //        '会员积分兑换订单' => '',
    ];

    /**
     * 活动/优惠券支付方式配置，格式：活动名 => [DP支付名称,金蝶支付编码]
     * @var array[]
     */
    protected $payment_activity_cfg = [
        '优惠券' => ['TCMP-COUP', 'TCMPC'],
        '商家优惠' => ['视频号-商家优惠', 'CHNSJ'],
        '达人优惠' => ['视频号-达人优惠', 'CHNDR'],
    ];

    public function mapping_payment_code($params)
    {
        switch ($params['category']) {
            case 'kol_discount':  // 达人优惠
                $result = 'CHNDR';
                break;
            case 'platform_discount': // 平台优惠
                switch ($params['sub_type']) {
                    case 'coupon':  // 优惠券
                        $result = 'TCMPC';
                        break;
                    default:
                        $result = 'unkown';
                        break;
                }
                break;
            case 'shop_discount': // 商家优惠
                switch ($params['sub_type']) {
                    case 'coupon':  // 优惠券
                        $result = 'TCMPC';
                        break;
                    case 'discount': // 商家优惠
                    case 'adjust': // 商家改价
                        $result = 'CHNSJ';
                        break;
                }
                break;
        }
        return $result;
    }

    public function mapping_payment_name($params)
    {
        switch ($params['category']) {
            case 'kol_discount':  // 达人优惠
                $result = '视频号-达人优惠';
                break;
            case 'platform_discount': // 平台优惠
                switch ($params['sub_type']) {
                    case 'coupon':  // 优惠券
                        $result = 'TCMP-COUP';
                        break;
                    default:
                        $result = '视频号-平台优惠';
                        break;
                }
                break;
            case 'shop_discount': // 商家优惠
                switch ($params['sub_type']) {
                    case 'coupon':  // 优惠券
                        $result = 'TCMP-COUP';
                        break;
                    case 'discount': // 商家优惠
                    case 'adjust': // 商家改价
                        $result = '视频号-商家优惠';
                        break;
                }
                break;
        }
        return $result;
    }

    /**
     * 获取计算优惠金额的类型字段
     * @return mixed
     */
    public function getDiscountFields()
    {
        # 优惠券、活动折扣
        return ['coupon', 'discount'];
    }
}
