<?php

class erpapi_waplogistics_matrix_xhs_config extends erpapi_logistics_matrix_config{

    public function get_query_params($method, $params){
        $shopId = $this->__channelObj->channel['shop_id'];
        $shop = app::get('ome')->model('shop')->dump(array('shop_id'=>$shopId), 'node_type,node_id');
        $query_params = array(
            'to_node_id' => $shop['node_id'],
            'node_type' => $shop['node_type'],
        );
        if($method == STORE_USER_TEMPLATE) {
            unset($query_params['company_code']);
        }
        $pqp = parent::get_query_params($method, $params);
        $query_params = array_merge($pqp, $query_params);
        return $query_params;
    }

    public function get_to_node_id()
    {
        $shopId = $this->__channelObj->channel['shop_id'];
        $shop = app::get('ome')->model('shop')->dump(array('shop_id'=>$shopId), 'node_id');

        return $shop['node_id'];
    }
}