<?php

class erpapi_waplogistics_matrix_xhs_request_electron extends erpapi_waplogistics_request_electron
{
    /**
     * 小红书电子面单
     * @param $sdf
     * @return mixed
     */
    public function directRequest($sdf)
    {
        $this->title     = '小红书-' . $this->__channelObj->channel['logistics_code'] . '获取电子面单';
        $this->timeOut   = 20;
        $this->primaryBn = $sdf['primary_bn'];

        $xhsObj = kernel::single('wap_event_trigger_logistics_data_electron_xhs');

        $custom_mark = $mark_text = [];
        foreach ($sdf['order'] as $k => $v) {
            if ($v['custom_mark']) {
                $tmp           = unserialize($v['custom_mark']);
                $custom_mark[] = str_replace(["\t", "\r\n", "\r", "\n", "'", "\"", "\\"], '', $tmp['op_content']);
            }
            if ($v['mark_text']) {
                $tmp         = unserialize($v['mark_text']);
                $mark_text[] = str_replace(["\t", "\r\n", "\r", "\n", "'", "\"", "\\"], '', $tmp['op_content']);
            }
        }

        $params           = [];
        $params['cpCode'] = $this->__channelObj->channel['logistics_code']; // 快递公司编码
        // 是否顺丰，顺丰需要特殊处理
        $is_sf = in_array($this->__channelObj->channel['logistics_code'], ['SF', 'FOP']);
        if ($is_sf) {
            $params['cpCode'] = 'shunfeng';
        }

        // 寄件人地址必须和订购关系的地址保持一致
        $params['sender'] = json_encode([
            'address' => [
                'city'     => $sdf['shop']['city'],
                'detail'   => $sdf['shop']['address_detail'],
                'district' => $sdf['shop']['area'],
                'province' => $sdf['shop']['province'],
                'town'     => $sdf['shop']['street'],
            ],
            'mobile'  => $sdf['shop']['mobile'], // 手机号码，明文
            'name'    => $sdf['shop']['default_sender'], // 姓名，明文
            'phone'   => $sdf['shop']['tel'],
        ]);


        $serviceCode = json_decode($this->__channelObj->channel['service_code'], true);

        $logisticsServices = $product_type = [];
        foreach ($serviceCode as $k => $v) {
            if (in_array($k, ['customerCode', 'branchCode'])) {
                continue;
            }
            if ($k == 'PRODUCT-TYPE') {
                $product_type = $v['value'];
                continue;
            }
            if ($v['value'] == '1') {
                $logisticsServices[$k] = new stdClass;
            }
        }

        $orderInfo = [
            'orderChannelsType' => $xhsObj->orderChannelsType($sdf['shop']['shop_type']),
            'tradeOrderList'    => is_array($sdf['order_bns']) ? $sdf['order_bns'] : [$sdf['order_bns']],
        ];
        $custom_mark && $orderInfo['buyerMemo'] = $custom_mark;
        $mark_text && $orderInfo['sellerMemo']  = $mark_text;

        // 订单号需要增加随机数，保证每次请求的订单号不同
        $tid = substr($sdf['primary_bn'], 0, strlen($sdf['primary_bn']) - 6) . date('His');
        // 发货单商品列表
        $delivery_items = $this->formatDeliveryItems($sdf['delivery_item']);
        if(empty($delivery_items)) {
            $result[] = array(
                'succ' => false,
                'msg' => '发货单商品列表为空',
                'delivery_id' => $sdf['delivery']['delivery_id'],
                'delivery_bn' => $sdf['delivery']['delivery_bn'],
            );
            return $result;
        }

        // 请求面单列表（上限10个）
        $params['tradeOrderInfoList']   = [];
        $params['tradeOrderInfoList'][0] = [
            'objectId'    => $tid, // 请求ID，保证一次批量请求不重复，返回结果基于该值取到对应的快递单号
            'orderInfo'   => $orderInfo,
            'templateId'  => 0,
            'packageInfo' => [
                'id'                   => $sdf['delivery']['delivery_id'], // 包裹ID
                'items'                => $delivery_items, // 默认空，下面会赋值
                'volume'               => 0, // 体积, 单位 ml
                'weight'               => 0, // 重量,单位 g
                'length'               => 0, // 包裹长，单位厘米
                'width'                => 0, // 包裹宽，单位厘米
                'height'               => 0, // 包裹高，单位厘米
                'totalPackagesCount'   => 0, // 子母件包裹数
                'packagingDescription' => '', // 大件快运的包装方式描述
                'goodsDescription'     => '', // 大件快运的货品描述,顺丰要求必传长度不能超过20,且不能和商品名称相同
                'goodValue'            => 0, // 物流价值，单位元
            ],
        ]; 

        // 月结卡号，直营快递公司一般要求必填
        if (!empty($sdf['monthly_account'])) {
            $params['customerCode'] = $sdf['monthly_account'];
        }

        // 品牌编码，顺丰要求必填，其他快递不传或者空字符串
        if ($is_sf) {
            $params['brandCode'] = $this->__channelObj->channel['logistics_code'];
        }

        // 产品编码，京东要求必填，顺丰不传值则默认是1-顺丰特快，仅部分快递公司支持传入
        $product_type = empty($sdf['product_type']) ? $product_type : $sdf['product_type'];

        // 是否预约上门，仅顺丰支持传入
        $params['callDoorPickUp'] = 'false';

        // 预约上门取件时间，仅支持顺丰
        if (!empty($sdf['pickup_time']) && $sdf['pickup_time'] != 'now' && $is_sf) {
            $params['callDoorPickUp'] = 'true';
            $begin_time = strtotime(date('Y-m-d') . ' ' . $sdf['pickup_time'] . ':00');
            $end_time = $begin_time + 3600;
            // 预约上门取件时间，'yyyy-MM-dd HH:mm:ss'，仅部分快递公司支持传入
            $params['doorPickUpTime'] = date('Y-m-d H:i:s', $begin_time);
            // 预约上门取件截止时间，'yyyy-MM-dd HH:mm:ss'，仅部分快递公司支持传入
            $params['doorPickUpEndTime'] = date('Y-m-d H:i:s', $end_time);
        }

        // 店铺名称，对参数内容没有限制，不会做校验，但是要求必传
        $params['sellerName'] = $sdf['shop']['shop_name'];
        // 网点编码，加盟型快递公司要求必填，直营快递（顺丰 、邮政、京东、德邦等）传空字符
        $params['branchCode'] = '';
        // 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单
        //$params['billVersion'] = 2;
        // 付款方式；注意仅新版电子面单支持；顺丰，1:寄方付 2:收方付 3:第三方付；邮政（包括youzhengguonei、ems、youzhengbiaokuai）：1寄付，2-到付
        $params['payMethod'] = 3;


        $logisticsServices && $params['tradeOrderInfoList'][0]['logisticsServices'] = json_encode($logisticsServices); // 物流服务

        $recipient = [
            'address'       => [
                'city'     => $sdf['delivery']['ship_city'],
                'detail'   => $sdf['delivery']['ship_addr'],
                'district' => $sdf['delivery']['ship_district'],
                'province' => $sdf['delivery']['ship_province'],
                'town'     => '',
            ],
            'mobile'        => $sdf['delivery']['ship_mobile'],
            'name'          => $sdf['delivery']['ship_name'],
            'phone'         => $sdf['delivery']['ship_tel'],
            'openAddressId' => $sdf['order'][0]['order_extend']['extend_field']['openAddressId'] ?: '',
        ];
        $is_encrypt = kernel::single('ome_security_router', $sdf['delivery']['shop_type'])->is_encrypt($sdf['delivery'], 'delivery');
        if ($is_encrypt) {
            $original = app::get('ome')->model('order_receiver')->db_dump(['order_id' => $sdf['delivery']['delivery_order'][0]['order_id']], 'encrypt_source_data');
            if ($original) {
                $encrypt_source_data = json_decode($original['encrypt_source_data'], 1);
                if ($encrypt_source_data) {
                    $recipient = [
                        'address'       => [
                            'city'     => $sdf['delivery']['ship_city'],
                            'detail'   => explode('>>', $sdf['delivery']['ship_addr'])[0],
                            'district' => $sdf['delivery']['ship_district'],
                            'province' => $sdf['delivery']['ship_province'],
                            'town'     => '',
                        ],
                        'mobile'        => $encrypt_source_data['buyer_mobile_index_origin'],
                        'name'          => explode('>>', $sdf['delivery']['ship_name'])[0],
                        'phone'         => $encrypt_source_data['buyer_phone_index_origin'],
                        'openAddressId' => $sdf['order'][0]['order_extend']['extend_field']['openAddressId'] ?: '',
                    ];
                }
            }
        }

        // 和小红书沟通，如果有openAddressId，就不传detail，因为传了小红书有可能会返回收货地址无效
        if ($recipient['openAddressId']) {
            $recipient['address']['detail'] = '';
        }

        if ($recipient['mobile'] == $recipient['openAddressId']) {
            $recipient['mobile'] = '';
        }
        if ($recipient['name'] == $recipient['openAddressId']) {
            $recipient['name'] = '';
        }
        if ($recipient['phone'] == $recipient['openAddressId']) {
            $recipient['phone'] = '';
        }

        $params['tradeOrderInfoList'][0]['recipient'] = $recipient;
        $params['tradeOrderInfoList'] = json_encode($params['tradeOrderInfoList']);

        $result = $this->requestCall(STORE_WAYBILL_GET, $params, array());
        # 记录发件人信息
        $sdf['sender_info'] = $sdf['shop'];
        $sdf['unique_code'] = $tid;  // 请求唯一单据号
        return $this->backToResult($sdf['channel_id'] ?? null, $result, $sdf);
    }


    /**
     * 返回结果处理
     * @param $channel_id
     * @param $ret
     * @param $sdf
     * @return array
     */
    private function backToResult($channel_id, $ret, $sdf)
    {
        $result = array();
        $waybill = empty($ret['data']) ? array() : json_decode($ret['data'], true);
        # 发货单信息
        $delivery = $sdf['delivery'];
        if (empty($waybill) || $ret['rsp'] == 'fail') {
            $result[] = [
                'succ' => false,
                'msg' => !empty($ret['msg']) ? $ret['msg'] : '获取电子面单失败',
                'delivery_id' => $delivery['delivery_id'],
                'delivery_bn' => $delivery['delivery_bn'],
            ];
            return $result;
        }
        # 转换发货人信息
        if (!empty($sdf['sender_info']) && is_string($sdf['sender_info'])) {
            $sdf['sender_info'] = json_decode($sdf['sender_info'], true);
        }
        $result[] = array(
            'succ' => $waybill['waybill_id'] ? true : false,
            'msg' => '',
            'delivery_id' => $delivery['delivery_id'],
            'delivery_bn' => $delivery['delivery_bn'],
            'logi_no' => $waybill['waybill_id'],
            'logistics_code' => $delivery['logistics_code'],
            'mailno_barcode' => '',
            'qrcode' => '',
            'position' => '',
            'position_no' => '',
            'package_wdjc' => '',
            'package_wd' => '',
            'print_config' => json_encode(['ewaybill_order_id' => $waybill['ewaybill_order_id']]),
            'json_packet' => is_array($waybill) ? json_encode($waybill, JSON_UNESCAPED_UNICODE) : $waybill,
            'send_info' => $sdf['sender_info'],
            'customer_code' => $sdf['customer_code'],         // 客户编码
            'monthly_account' => $sdf['monthly_account'],     // 月结号
            'monthly_type' => $sdf['monthly_type'],           // 月结号类型
            'product_type' => $sdf['product_type'],           // 服务类型
            'pickup_time' => $sdf['pickup_time'],             // 上门取件时间
            'seller_mobile' => $sdf['sender_info']['mobile'], // 发件人手机号
            'unique_code' => $sdf['unique_code'], // 请求唯一单据号
        );
        $this->directDataProcess($result, $channel_id);
        return $result;
    }

    /**
     * 取消电子面单
     * @param $waybillNumber
     * @param $delivery_bn
     * @return mixed
     */
    public function recycleWaybillNew($sdf)
    {
        $waybillModel = app::get('logisticsmanager')->model('waybill');
        # 更新状态
        $waybillModel->update(array('status' => 2, 'create_time' => time()), array('id' => $sdf['waybill_id']));

        $this->primaryBn = empty($sdf['delivery_bn']) ? $sdf['waybill_number'] : $sdf['delivery_bn'];
        $this->title = '小红书_' . $sdf['company_code'] . '取消电子面单';

        # 参数
        $params = array(
            'cpCode' => $this->__channelObj->channel['logistics_code'],
            'waybillCode' => $sdf['waybill_number'], // 快递单号
        );
        // 是否顺丰，顺丰需要特殊处理
        $is_sf = in_array($this->__channelObj->channel['logistics_code'], ['SF', 'FOP']);
        if ($is_sf) {
            $params['cpCode'] = 'shunfeng';
        }

        // 电子面单版本号，1-默认值旧版电子面单 2-新版电子面单
        //$params['billVersion'] = 2;

        $callback = array(
            'class' => get_class($this),
            'method' => 'callback',
            'params' => $sdf,
        );
        # 设置拦截回传
        if (!empty($sdf['interception_id'])) {
            $callback['method'] = 'intercetioncancelBack';
        }
        return $this->requestCall(STORE_WAYBILL_CANCEL, $params, $callback);
    }


    /**
     * 格式化发货单商品列表
     * @param $delivery_item
     * @return array
     */
    private function formatDeliveryItems($delivery_item)
    {
        if (empty($delivery_item)) {
            return [];
        }

        $result = [];
        foreach ($delivery_item as $k => $v) {
            $result[] = [
                'count' => $v['number'],
                'name'  => $v['product_name'],
            ];
        }

        return $result;
    }
}
