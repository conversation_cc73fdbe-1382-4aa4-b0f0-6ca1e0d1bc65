<?php

/**
 * User: jintao Date: 2016/7/6
 */
class erpapi_logistics_matrix_hqepay_request_electron extends erpapi_waplogistics_request_electron
{

    public function bufferRequest($sdf)
    {
        return $this->directNum;
    }

    public function directRequest($sdf)
    {
        $delivery = $sdf['delivery'];
        $shopInfo = $sdf['shop'];
        $this->primaryBn = $delivery['delivery_bn'];

        $from_address = $shopInfo['address_detail'] ? $shopInfo['province'] . $shopInfo['city'] . $shopInfo['area'] . $shopInfo['address_detail'] : '_SYSTEM';
        $service_list = array(// array('name' => 'SafeMail', 'value' => '1'), // 隐私面单
        );

        # 订单号需要增加随机数，保证每次请求的订单号不同
        $tid = substr($delivery['delivery_bn'], 0, strlen($delivery['delivery_bn']) - 6) . date('His');

        $params = array(
            "member_id" => $delivery['member_id'],
            "send_site" => "",  # 收件网点标识
            "company_code" => $this->__channelObj->channel['logistics_code'],  # 物流公司编码
            "logistic_code" => '',  # 运单号
            "tid" => $tid,  # 订单号
            "exp_type" => $sdf['product_type'],  # 快递类型 1是标准快件
            "cost" => '',
            "other_cost" => '',

            'to_company' => $this->charFilter($delivery['ship_name']),
            'to_name' => $delivery['consignee']['name'],#收货人
            'to_tel' => $delivery['consignee']['telephone'] ?? '',
            'to_mobile' => $delivery['consignee']['mobile'],
            'to_zip' => $delivery['consignee']['zip'],
            'to_province' => $delivery['consignee']['province'],
            'to_city' => $delivery['consignee']['city'],
            'to_area' => $delivery['consignee']['district'],
            'to_address' => $delivery['consignee']['addr'],

            'from_company' => '',
            'from_name' => $shopInfo['default_sender'] ? $shopInfo['default_sender'] : '_SYSTEM',
            'from_tel' => $shopInfo['tel'],
            'from_mobile' => $shopInfo['mobile'],
            'from_zip' => $shopInfo['zip'],
            'from_province' => $shopInfo['province'],
            'from_city' => $shopInfo['city'],
            'from_area' => $shopInfo['area'],
            'from_address' => $this->charFilter($from_address),#发件人详细地址

            'is_notice' => $sdf['extend']['is_notice'] ?? 0, // 0：通知，1：不通知
            "start_date" => '',#上门取货时间段
            "end_date" => '',
            "weight" => '',
            "volume" => '',
            "remark" => '',
            "qty" => "",
            'service_list' => json_encode($service_list),
            "goods_list" => json_encode($this->format_delivery_item($sdf['delivery_item'])),#货品明细信息
            "is_return" => '0',  # 返回电子面单模板：0-不需要；1-需要
            'is_print_orgin' => '1', // 调用增值服务失败后是否调用原始电子面单

            "pay_type" => '3', // 1：现付,2：到付,3：月结,4：第三方付
            "version" => $sdf['version'] ?? '', // 矩阵版本
        );

        # 扩展信息 - 京东需要这些字段
        if (!empty($sdf['extend'])) {
            $params['access_token'] = $sdf['extend']['access_token'] ?? '';
            $params['app_key'] = $sdf['extend']['app_key'] ?? '';
            $params['app_secret'] = $sdf['extend']['app_secret'] ?? '';
        }

        # 平台
        if (!empty($sdf['LogisticsRouteCode'])) {
            $params['LogisticsRouteCode'] = $sdf['LogisticsRouteCode'];
        }

        # 月结卡号
        if (!empty($sdf['monthly_account'])) {
            $params['month_code'] = $sdf['monthly_account'];
            $params['customer_code'] = $sdf['monthly_account'];
            $params['customer_name'] = $sdf['monthly_account'];
        }

        # 上门取件时间
        if (!empty($sdf['pickup_time']) && $sdf['pickup_time'] != 'now') {
            $begin_time = strtotime(date('Y-m-d') . ' ' . $sdf['pickup_time'] . ':00');
            $end_time = $begin_time + 3600;
            # 要求上门取件时间段  2020-11-11 22:00:00|2020-11-11 23:00:00
            $params['start_date'] = date('Y-m-d H:i:s', $begin_time);
            $params['end_date'] = date('Y-m-d H:i:s', $end_time);
        }

        // 是否加密
        $is_encrypt = false;
        if (!$is_encrypt) {
            $is_encrypt = kernel::single('ome_security_router', $delivery['shop_type'])->is_encrypt($delivery, 'delivery');
        }
        // 云鼎解密
        $gateway = '';
        if ($is_encrypt) {
            $params['s_node_id'] = $delivery['shop']['node_id'];
            $params['s_node_type'] = $delivery['shop_type'];
            // 新增解密字段
            $params['order_bns'] = implode(',', $delivery['order_bns']);
            $gateway = $delivery['shop_type'];
        }

        $back = $this->requestCall(STORE_HQEPAY_ORDERSERVICE, $params, array(), $sdf, $gateway);
        # 记录发件人信息
        $sdf['sender_info'] = $sdf['shop'];
        $sdf['unique_code'] = $tid;  // 请求唯一单据号
        return $this->backToResult($sdf['channel_id'] ?? null, $back, $sdf);
    }

    #获取货物名称
    public function format_delivery_item(&$deliveryItems = null)
    {
        $items = array();
        foreach ($deliveryItems as $key => $item) {
            $items[$key]['bn'] = str_replace('+', ' ', $item['bn']);
            $items[$key]['name'] =  str_replace(array("<", ">", "&", "'", '"', '', '+', '\\', '%'), '', $item['product_name']);
            $items[$key]['qty'] = $item['number'];
        }
        return $items;
    }

    public function backToResult($channel_id, $ret, $sdf)
    {
        $result = array();
        $waybill = empty($ret['data']) ? array() : json_decode($ret['data'], true);
        # 发货单信息
        $delivery = $sdf['delivery'];
        if (empty($waybill) || $ret['rsp'] == 'fail') {
            $result[] = [
                'succ' => false,
                'msg' => !empty($ret['msg']) ? $ret['msg'] : '获取电子面单失败',
                'delivery_id' => $delivery['delivery_id'],
                'delivery_bn' => $delivery['delivery_bn'],
            ];
            return $result;
        }

        if (empty($waybill['Order']['LogisticCode'])) {
            $msg = sprintf('%s-%s', $waybill['ResultCode'], $waybill['Reason']);
        }
        $json_packet = array(
            'ShipperCode' => (string)$waybill['Order']['ShipperCode'],
            'KDNOrderCode' => (string)$waybill['Order']['KDNOrderCode'],
            'DestinatioCode' => (string)$waybill['Order']['DestinatioCode'],//四段码
            'OriginCode' => (string)$waybill['Order']['OriginCode'],//末端分间编码
            'ShipperInfo' => $waybill['Order']['ShipperInfo'],
        );
        # 转换发货人信息
        if (!empty($sdf['sender_info']) && is_string($sdf['sender_info'])) {
            $sdf['sender_info'] = json_decode($sdf['sender_info'], true);
        }
        $result[] = array(
            'succ' => !empty($waybill['Order']['LogisticCode']),
            'msg' => $msg ?? '',
            'delivery_id' => $delivery['delivery_id'],
            'delivery_bn' => $delivery['delivery_bn'],
            'logi_no' => $waybill['Order']['LogisticCode'],
            'position' => $waybill['Order']['DestinatioName'] ?? '',
            'position_no' => $waybill['Order']['DestinatioCode'] ?? '',
            'package_wdjc' => $waybill['Order']['PackageName'] ?? '',
            'package_wd' => $waybill['Order']['PackageCode'] ?? '',
            'json_packet' => json_encode($json_packet, JSON_UNESCAPED_UNICODE),
            'logistics_code' => $json_packet['ShipperCode'],  // 物流公司编码
            'send_info' => $sdf['sender_info'] ?? [],
            'customer_code' => $sdf['customer_code'] ?? '',         // 客户编码
            'monthly_account' => $sdf['monthly_account'],     // 月结号
            'monthly_type' => $sdf['monthly_type'],           // 月结号类型
            'product_type' => $sdf['product_type'],           // 服务类型
            'pickup_time' => $sdf['pickup_time'],             // 上门取件时间
            'seller_mobile' => $sdf['sender_info']['mobile'] ?? '', // 发件人手机号
            'unique_code' => $sdf['unique_code'], // 请求唯一单据号
        );
        $this->directDataProcess($result, $channel_id);
        return $result;
    }

    /**
     * 取消电子面单
     * @param $sdf
     * @return array|null
     */
    public function recycleWaybillNew($sdf)
    {
        $waybillModel = app::get('logisticsmanager')->model('waybill');
        # 更新状态
        $waybillModel->update(array('status' => 2, 'create_time' => time()), array('id' => $sdf['waybill_id']));

        $this->primaryBn = $sdf['delivery_bn'];
        $this->title = '快递鸟_' . $sdf['company_code'] . '取消电子面单';

        # 参数
        $params = array(
            'tid' => $sdf['delivery_bn'], // 订单号
            'company_code' => $sdf['company_code'], // 快递公司编码
            'exp_no' => $sdf['waybill_number'], // 快递单号
            'customer_name' => $sdf['monthly_account'], // 月结卡号
        );
        # 请求唯一单据号
        if (!empty($sdf['unique_code'])) {
            $params['tid'] = $sdf['unique_code'];
        }
        # 京东要增加版本
        if (strtoupper($sdf['company_code']) == 'JD') {
            $params['version'] = 'jdl';
        }
        # 请求唯一单据号
        if (!empty($sdf['unique_code'])) {
            $params['tid'] = $sdf['unique_code'];
        }
        $callback = array(
            'class' => get_class($this),
            'method' => 'callback',
            'params' => $sdf,
        );
        # 设置拦截回传
        if (!empty($sdf['interception_id'])) {
            $callback['method'] = 'intercetioncancelBack';
        }
        return $this->requestCall(STORE_WAYBILL_CANCEL, $params, $callback);
    }
}
