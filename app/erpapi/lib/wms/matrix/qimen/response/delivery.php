<?php
/**
 * WMS 发货单
 *
 * @category 
 * @package 
 * <AUTHOR>
 * @version $Id: Z
 */
class erpapi_wms_matrix_qimen_response_delivery  extends erpapi_wms_response_delivery 
{
    /**
     * wms.delivery.status_update
     *
     **/
    public function status_update($params)
    {
        // 参数校验
        $this->__apilog['title']       = $this->__channelObj->wms['channel_name'] . '发货单[' . $params['delivery_bn'] . ']' . $params['status'];
        $this->__apilog['original_bn'] = $params['delivery_bn'];

        $logi_no = $params['logi_no'];

        if ($this->__channelObj->wms['adapter'] != 'selfwms' && $params['status'] == 'DELIVERY' && !$logi_no) {
            $this->__apilog['result']['msg'] = '缺少运单号';
            return false;
        }

        $extendProps = isset($params['extendProps']) ? json_decode($params['extendProps'], true) : array();
        $params['logistics_settlement_number'] = $extendProps['shipYuejieNumber'];
        $params['logistics_delivery_mobile'] = $extendProps['shipMobile'];

        $items = $params['item'] ? @json_decode($params['item'], true) : array();
        if (empty($items)) {
            $this->__apilog['result']['msg'] = '缺少发货明细';
            return false;
        }

        $deliveryInfo = app::get('ome')->model('delivery')->dump(array('delivery_bn' => $params['delivery_bn']), 'branch_id');
        $storeInfo = app::get('o2o')->model('store')->dump(array('branch_id' => $deliveryInfo['branch_id']), 'store_id');

        if ($items) {
            foreach ($items as $key => $val) {
                $material = app::get('material')->model('basic_material')->dump(array('brand_sku_code' => $val['product_bn'], 'store_id' => $storeInfo['store_id']), 'bm_id,material_bn,brand_sku_code');
                if (empty($material)) {
                    continue;
                }
                $items[$key]['product_bn'] = $material['material_bn'];
            }
        }

        $params['item'] = json_encode($items);
        $params = parent::status_update($params);
    }
}
