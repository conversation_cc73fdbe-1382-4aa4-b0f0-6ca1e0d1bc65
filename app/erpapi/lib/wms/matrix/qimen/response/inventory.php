<?php

/**
 * 盘点
 */
class erpapi_wms_matrix_qimen_response_inventory extends erpapi_wms_response_inventory
{
    protected $adjustType = [
        'ADJUST' => '1', // 全量
        'CHECK' => '2', // 增量
    ];

    /**
     * wms.inventory.add
     *
     **/
    public function add($params)
    {
        $items = isset($params['item']) ? json_decode($params['item'], true) : array();
        if (empty($items)) {
            $this->__apilog['result']['msg'] = '缺少对应的sku商品信息';
            return false;
        }

        # sku品牌编码
        $itemList = array();
        foreach ($items as $val) {
            if (!empty($val['product_bn'])) {
                $itemList[$val['product_bn']] = $val;
            }
        }
        if (empty($itemList)) {
            $this->__apilog['result']['msg'] = '缺少对应的sku商品信息';
            return false;
        }

        $basicMdl = app::get('material')->model('basic_material');
        $storeMdl = app::get('o2o')->model('store');
        # 根据sku品牌编码读取基础物料
        $brand_sku_codes = array_keys($itemList);
        $basicList = $basicMdl->getList('bm_id,material_bn,material_name,brand_sku_code,material_spu,store_id', array('brand_sku_code' => $brand_sku_codes));
        if (empty($basicList)) {
            $this->__apilog['result']['msg'] = 'sku品牌编码缺少对应的基础物料信息';
            return false;
        }
        # 基础物料中的品牌sku编码
        $basic_code_list = array_unique(array_column($basicList, 'brand_sku_code'));
        $diff_code_list = array_diff($brand_sku_codes, $basic_code_list);
        if (!empty($diff_code_list)) {
            $this->__apilog['result']['msg'] = '以下品牌sku编码对应的基础物料信息不存在：' . implode(',', $diff_code_list);
            return false;
        }

        # 查询门店
        $storeIds = array_unique(array_column($basicList, 'store_id'));
        $storeList = $storeMdl->getList('store_id,store_bn,stock_source', array('store_id' => $storeIds));
        if (!empty($storeList)) {
            $storeData = array_column($storeList, null, 'store_id');
        }

        // 参数校验
        $this->__apilog['title'] = $this->__channelObj->wms['channel_name'] . '库存盘点单：' . $params['inventory_bn'];
        $this->__apilog['original_bn'] = $params['inventory_bn'];

        # 是否库存查询
        $add_branch_freeze = app::get('wmsmgr')->getConf('add_branch_freeze_' . $this->__channelObj->wms['channel_id']);

        $data = array(
            'inventory_bn' => trim($params['inventory_bn']),
            'branch_bn' => $params['warehouse'],
            'memo' => $params['remark'],
            'operate_time' => $params['operate_time'] ?? date('Y-m-d H:i:s'),
            'wms_id' => $this->__channelObj->wms['channel_id'],
            'mode' => $this->adjustType[$params['adjustType']],  // 1: 全量, 2: 增量
            'add_branch_freeze' => $add_branch_freeze ? true : false,
            'items' => [],
        );

        if (!in_array($params['adjustType'], array_keys($this->adjustType))) {
            $this->__apilog['result']['msg'] = '盘点模式参数错误：' . $data['mode'];
            return false;
        }

        foreach ($basicList as $item) {
            if (empty($storeData[$item['store_id']]['store_bn'])) {
                $this->__apilog['result']['msg'] = 'sku品牌编码[' . $item['brand_sku_code'] . ']未查询到线下门店信息';
                return false;
            }

            # 库存来源，过滤非云仓
            $stockSource = $storeData[$item['store_id']]['stock_source'];
            if ($stockSource != 'wms') {
                continue;
            }

            $sku = [
                'bm_id' => $item['bm_id'],
                'spu_id' => $item['material_spu'],
                'material_bn' => $item['material_bn'],
                'material_name' => $item['material_name'],
                'store_bn' => $storeData[$item['store_id']]['store_bn'],
                'store' => $data['mode'] == '1' ? $itemList[$item['brand_sku_code']]['totalQty'] : $itemList[$item['brand_sku_code']]['normal_num'],
            ];
            $data['items'][] = $sku;
        }
        return $data;
    }
}
