<?php

/**
 * 盘点
 *
 * @category
 * @package
 * <AUTHOR>
 * @version $Id: Z
 */
class erpapi_wms_response_process_inventory
{
    protected $storeList = [];

    /**
     * 盘点
     *
     * @param Array $params =array(
     *                  'inventory_bn'=>@盘点单号@
     *                  'operate_time'=>@操作时间@
     *                  'memo'=>@备注@
     *                  'wms_id'=>@仓储id@
     *                  'io_source'=>selfwms
     *                  'branch_bn'=>@库存编号@
     *                  'inventory_type'=>@盘点类型@
     *                  'items'=>array(
     *                      'bn'=>@货号@
     *                      'num'=>@库存@
     *                      'normal_num'=>@良品@
     *                      'defective_num'=>@不良品@
     *                  )
     *              )
     *
     * @return mixed
     * <AUTHOR>
    public function add($sdf)
    {
        if (empty($sdf['items'])) {
            return array('rsp' => 'fail', 'msg' => '库存信息不能为空');
        }

        $materialObj = kernel::single('material_kucun100');
        $storeMdl = app::get('o2o')->model('store');
        $branchMdl = app::get('ome')->model('branch');
        $oBranchPro = app::get('ome')->model('branch_product');
        $stockLogMdl = app::get('material')->model('stock_sync_log');
        $basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');

        $warnList = [];
        # 读取库存预警设置
        $warningOpen = app::get('ome')->getConf('ome.store.warning.open');
        # 获取商品列表
        foreach ($sdf['items'] as $param) {
            # 获取门店信息
            if (empty($this->storeList[$param['store_bn']])) {
                $storeInfo = $storeMdl->dump(array('store_bn' => $param['store_bn']), 'store_id,store_bn');
                if (!empty($storeInfo)) {
                    $this->storeList[$param['store_bn']] = $storeInfo;
                }
            }
            $storeInfo = $this->storeList[$param['store_bn']];
            # 门店信息不存在无需处理
            if (empty($storeInfo)) {
                return array('rsp' => 'fail', 'msg' => '门店编码[' . $param['store_bn'] . ']对应的门店信息不存在');
            }

            # 获取仓库
            if (empty($this->storeList[$param['store_bn']]['branch_id'])) {
                $branchInfo = $branchMdl->dump(array('store_id' => $storeInfo['store_id']), 'branch_id');
                if (!empty($branchInfo)) {
                    $this->storeList[$param['store_bn']]['branch_id'] = $branchInfo['branch_id'];
                }
            }
            $branch_id = $this->storeList[$param['store_bn']]['branch_id'];
            # 没有门店对应的仓库无需处理
            if (empty($branch_id)) {
                return array('rsp' => 'fail', 'msg' => '门店编码[' . $param['store_bn'] . ']对应的仓库信息不存在');
            }

            # 查询库存
            $branchPro_info = $oBranchPro->dump(array('branch_id' => $branch_id, 'product_id' => $param['bm_id']), 'store,unit_cost');
            if (empty($branchPro_info)) {
                $branchPro_info['store'] = 0;
                $branchPro_info['unit_cost'] = 0;
            }

            # 检查库存数
            $store = intval($param['store']) < 0 ? 0 : $param['store'];
            # 订单冻结库存
            $store_freeze = $basicMStockFreezeLib->getShopFreezeByBmid($param['bm_id']);
            # 仓库冻结库存
            $branch_freeze = $basicMStockFreezeLib->getBranchFreezeByBmid($param['bm_id']);

            # 是否增加冻结库存
            if ($sdf['add_branch_freeze']) {
                # 查询30天内的订单冻结库存
                $day = strtotime('-30 days');
                $sql = "SELECT SUM(b.number) AS number FROM sdb_ome_delivery a LEFT JOIN sdb_ome_delivery_items b ON a.delivery_id = b.delivery_id"
                    . " WHERE a.status IN ('ready', 'progress') AND a.sync_status = '3' AND a.parent_id = 0"
                    . " AND a.order_createtime > " . $day . " AND b.product_id ={$param['bm_id']}";
                $delivery_freeze = kernel::database()->select($sql);
                if (!empty($delivery_freeze[0]['number'])) {
                    $store += $delivery_freeze[0]['number'];
                }
            }

            # 记录库存日志
            if (!empty($warningOpen) && $warningOpen == 'true') {
                $log_data = [
                    'store_bn' => $param['store_bn'],
                    'bm_id' => $param['bm_id'],
                    'num' => $store,
                    'mode' => $sdf['mode'],
                    'order_freeze' => $store_freeze,
                    'branch_freeze' => $branch_freeze,
                    'warning_flag' => 'wait',
                    'warning_status' => 'wait',
                    'source' => '奇门库存同步',
                    'createtime' => time()
                ];
                $stockLogMdl->save($log_data);
            }

            # 库存100推送库存时，如果同步过来的库存小于OMS订单预占+仓库冻结，就需要预警 <EMAIL>
            if ($sdf['mode'] == '1' && bccomp($store_freeze + $branch_freeze, $store) > 0) {
                $tmp_data = [
                    'store_bn' => $param['store_bn'],
                    'bm_id' => $param['bm_id'],
                    'bn' => $param['material_bn'],
                    'material_name' => $param['material_name'],
                    'num' => $store,
                    'order_freeze' => $store_freeze,
                    'branch_freeze' => $branch_freeze,
                    'createtime' => time()
                ];
                $warnList[] = $tmp_data;
            }

            # 判断原数量和修改的数量是否相同，模式：1: 全量, 2: 增量
            if ($sdf['mode'] == 1) {
                $diff_nums = $branchPro_info['store'] - $store;
                $type = $diff_nums < 0 ? "IN" : "OUT";
            } else {
                $diff_nums = $store;
                $type = "IN";
            }
            if ($diff_nums == 0) {
                continue;
            }

            if ($type == 'IN') {
                $adjustLib = kernel::single('siso_receipt_iostock_adjustin');
                $adjustLib->_typeId = 70;
            } else {
                $adjustLib = kernel::single('siso_receipt_iostock_adjustout');
                $adjustLib->_typeId = 7;
            }

            # 增加出入库明细
            $data = array();
            $data['product_id'] = $param['bm_id'];
            $data['branch_id'] = $branch_id;
            $data['to_nums'] = $store;

            $iostockData = array();
            $iostockData['branch_id'] = $data['branch_id'];
            $iostockData['operator'] = 'system';
            $iostockData['bill_type'] = 'branchadjust';
            $iostockData['original_bn'] = $sdf['inventory_bn'];
            $iostockData['items'][] = array(
                'bn' => $param['material_bn'],
                'iostock_price' => $branchPro_info['unit_cost'],
                'oper' => 'system',
                'nums' => abs($diff_nums),
                'memo' => '',
            );
            $error_msg = '';
            $adjustLib->create($iostockData, $data, $error_msg);

            # 更新仓库库存
            $materialObj->updateBranchProduct($store, $param['bm_id'], $branch_id, $storeInfo);
            # 更新基础物料库存
            $materialObj->updateProduct($store, $param['bm_id']);
        }

        # 库存100推送库存时，如果同步过来的库存小于OMS订单预占+仓库冻结，就需要预警 <EMAIL>
        if (!empty($warnList)) {
            $queueParams = array(
                'data' => array(
                    'params' => serialize($warnList),
                    'bill_type' => 'stock_warn',
                    'log_id' => time(),
                    'task_type' => 'sendemail',
                ),
                'url' => kernel::openapi_url('openapi.autotask', 'service'),
            );
            kernel::single('taskmgr_interface_connecter')->push($queueParams);
        }
        return array('rsp' => 'succ');
    }

    public function updateHangTotal($inventory_apply_id)
    {
        $oInventoryItem = app::get('console')->model("inventory_apply_items");
        $item = $oInventoryItem->db_dump(['inventory_apply_id' => $inventory_apply_id], 'sum(wms_stores) as sku_total, count(item_id) as sku_hang');
        $rs = app::get('console')->model("inventory_apply")->update(['sku_total' => $item['sku_total'], 'sku_hang' => $item['sku_hang']], array('inventory_apply_id' => $inventory_apply_id, 'status' => 'unconfirmed'));
        return is_bool($rs) ? false : true;
    }

    public function inventory_queue($inventory_id)
    {
        $inventory_ids = array();
        $inventory_ids[0] = $inventory_id;

        //获取system账号信息
        $opinfo = kernel::single('ome_func')->get_system();

        //自动审单_批量日志
        $blObj = app::get('ome')->model('batch_log');

        $batch_number = count($inventory_ids);
        $bldata = array(
            'op_id' => $opinfo['op_id'],
            'op_name' => $opinfo['op_name'],
            'createtime' => time(),
            'batch_number' => $batch_number,
            'log_type' => 'confirm_inventory',
            'log_text' => serialize($inventory_ids),
        );
        $result = $blObj->save($bldata);

        //自动审批任务队列(改成多队列多进程)
        if (defined('SAAS_COMBINE_MQ') && SAAS_COMBINE_MQ == 'true') {
            $data = array();
            $data['spider_data']['url'] = kernel::openapi_url('openapi.autotask', 'service');

            $push_params = array(
                'log_text' => $bldata['log_text'],
                'log_id' => $bldata['log_id'],
                'task_type' => 'confirminventory',
            );
            $push_params['taskmgr_sign'] = taskmgr_rpc_sign::gen_sign($push_params);
            foreach ($push_params as $key => $val) {
                $postAttr[] = $key . '=' . urlencode($val);
            }

            $data['spider_data']['params'] = empty($postAttr) ? '' : join('&', $postAttr);
            $data['relation']['to_node_id'] = base_shopnode::node_id('ome');
            $data['relation']['from_node_id'] = '0';
            $data['relation']['tid'] = $bldata['log_id'];
            $data['relation']['to_url'] = $data['spider_data']['url'];
            $data['relation']['time'] = time();

            $routerKey = 'tg.order.inventory.' . $data['relation']['from_node_id'];

            $message = json_encode($data);
            $mq = kernel::single('base_queue_mq');
            $mq->connect($GLOBALS['_MQ_COMBINE_CONFIG'], 'TG_COMBINE_EXCHANGE', 'TG_COMBINE_QUEUE');
            $mq->publish($message, $routerKey);
            $mq->disConnect();
        } else {
            $push_params = array(
                'data' => array(
                    'log_text' => $bldata['log_text'],
                    'log_id' => $bldata['log_id'],
                    'task_type' => 'confirminventory',
                ),
                'url' => kernel::openapi_url('openapi.autotask', 'service'),
            );

            kernel::single('taskmgr_interface_connecter')->push($push_params);
        }

        return true;
    }
}
