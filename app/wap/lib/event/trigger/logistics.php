<?php

class wap_event_trigger_logistics
{
    /**
     * 获取电子面单
     * @param $params
     * @return array|string[]|null
     */
    public function getWaybill($params)
    {
        $result = ['rsp' => 'succ'];
        $directRet = ['succ' => [], 'fail' => []];

        # 检查发货单状态
        $msg = '';
        $type = 'electron';
        $dlyInfo = kernel::single("wap_delivery")->checkDeliveryPrint($params['delivery_id'], $type, $msg);
        if (!$dlyInfo) {
            $result['res'] = 'fail';
            $result['msg'] = $msg;
            return $result;
        }

        $o2oElectronObj = kernel::single('o2o_event_trigger_logistics_electron');
        $wapDlyMdl = app::get('wap')->model('delivery');
        $funcObj = kernel::single('logisticsmanager_waybill_func');

        # 保存快递公司
        $corp_filter = [
            'type' => $params['logi_code'],
            'disabled' => 'false',
        ];
        $corpInfo = app::get('ome')->model('dly_corp')->dump($corp_filter, 'corp_id,type,name');
        if (!empty($corpInfo)) {
            $updateData = [
                'logi_id' => $corpInfo['corp_id'],
                'logi_name' => $corpInfo['name']
            ];
            $wapDlyMdl->update($updateData, array('delivery_id' => $params['delivery_id']));
        }

        # 物流公司编码转换
        if (!empty($params['logi_code'])) {
            $params['logi_code'] = $funcObj->getLogisticsCode($dlyInfo['shop_type'], $params['logi_code']);
        }

        switch ($dlyInfo['shop_type']) {
            case 'luban': // 抖音
                $rsp = $this->_lubanWaybill($params, $dlyInfo);
                break;
            case 'wxshipin': // 微信视频号
                $rsp = $this->_wxshipinWayBill($params, $dlyInfo);
                break;
            case 'ecos.ecshopx': // 微信小程序
                $rsp = $this->_ecshopxWayBill($params, $dlyInfo);
            case 'xhs': // 小红书
                $rsp = $this->_xhsWayBill($params, $dlyInfo);
                break;
        }
        if (!empty($rsp) && $rsp['rsp'] == 'fail') {
            $result['res'] = 'fail';
            $result['msg'] = $rsp['msg'];
            return $result;
        }

        # 保存运单号
        $backRet = $o2oElectronObj->directCallback($rsp);
        if (!empty($backRet['succ'])) {
            foreach ($backRet['succ'] as $val) {
                $directRet['succ'][] = $val;
            }
        }
        if (!empty($backRet['fail'])) {
            foreach ($backRet['fail'] as $val) {
                $directRet['fail'][] = $val;
            }
        }

        # 返回成功的信息
        if (empty($directRet['succ']) && !empty($directRet['fail'])) {
            $result['rsp'] = 'fail';
            # 错误信息
            $errorList = array_column($directRet['fail'], 'msg');
            $result['msg'] = empty($errorList) ? '获取电子面单失败，请联系管理员或技术人员处理' : implode(';', $errorList);
        }
        $result['data'] = $directRet;
        return $result;
    }

    /**
     * 抖音电子面单
     * @param $params
     * @param $dlyInfo
     * @return array
     */
    private function _lubanWaybill($params, $dlyInfo)
    {
        $branchObj = kernel::single('ome_branch');
        $channelMdl = app::get('logisticsmanager')->model('channel');
        $funcObj = kernel::single('logisticsmanager_waybill_func');
        $objChannelExt = app::get('logisticsmanager')->model('channel_extend');

        $result = ['rsp' => 'succ'];
        # 渠道参数
        $channelData = [
            'shop_id' => $dlyInfo['shop_id'],
            'company_code' => $params['logi_code'],
        ];

        # 判断是否使用小镇月结账号
        if (!empty($params['monthly_account']) && $params['monthly_account'] == 'default') {
            $channel_type = $funcObj->getShopType($dlyInfo['shop_type']);
            # 获取电子面单来源
            $filter = [
                'channel_type' => $channel_type,
                'shop_id' => $dlyInfo['shop_id'],
                'status' => 'true',
                'logistics_code' => $params['logi_code']
            ];
            $channelInfo = $channelMdl->dump($filter, 'channel_id,service_code');
            if (empty($channelInfo)) {
                $result['rsp'] = 'fail';
                $result['msg'] = '小镇未配置电子面单来源信息';
                return $result;
            }

            # 获取小镇的月结卡号
            $channelExt = $objChannelExt->dump(array('channel_id' => $channelInfo['channel_id']), 'channel_id,monthly_account');
            # 获取门店的发件人信息
            $o2oStoreInfo = $branchObj->getStoreSenderInfoByBranchId($dlyInfo['branch_id']);
            if (empty($o2oStoreInfo['consignee']['name']) || empty($o2oStoreInfo['consignee']['addr'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置发件人信息';
                return $result;
            }

            # 读取月结账号
            $serviceCode = json_decode($channelInfo['service_code'], true);

            $sendInfo = [
                'consignee' => [
                    'name' => $o2oStoreInfo['consignee']['name'],
                    'province' => $o2oStoreInfo['consignee']['province'],
                    'city' => $o2oStoreInfo['consignee']['city'],
                    'district' => $o2oStoreInfo['consignee']['district'],
                    'town' => $o2oStoreInfo['consignee']['town'] ?? '',
                    'addr' => $o2oStoreInfo['consignee']['addr'],
                    'telephone' => $o2oStoreInfo['consignee']['telephone'],
                    'mobile' => $o2oStoreInfo['consignee']['mobile'],
                    'zip' => $o2oStoreInfo['consignee']['zip'] ?? '000000',
                ],
                'service' => [
                    'channel_id' => $channelInfo['channel_id'],
                    'monthly_type' => 'village',  // 月结号类型  village：小镇，store：门店
                    'monthly_account' => $channelExt['monthly_account'] ?? $serviceCode['monthly_account'],  // 月结账号
                    'pickup_time' => $params['pickup_time'] ?? 'now',  // 上门取件时间
                    'service_list' => [],
                ]
            ];
            # 所有渠道获取电子面单时，发件人名称后面拼接对应-渠道（需要做个校验，如果名称里含渠道名称，不用再增加）
            $channelName = $funcObj->getChannelName($dlyInfo['shop_type'], $sendInfo['consignee']['name']);
            if (!empty($channelName)) {
                $sendInfo['consignee']['name'] = $channelName;
            }
            # 产品类型
            if (!empty($serviceCode['PRODUCT-TYPE'])) {
                $sendInfo['service']['product_type'] = $serviceCode['PRODUCT-TYPE']['value'];
            }
            $logisticsServices = array();
            foreach ($serviceCode as $k => $v) {
                if (in_array($k, array('monthly_account', 'PRODUCT-TYPE'))) {
                    continue;
                }
                //音尊达
                if ($k == 'SVC-WBHOMEDELIVERY' && $v['value'] == '1') {
                    $logisticsServices[] = [
                        'service_code' => $k,
                        'service_value' => json_encode(array('value' => ''))
                    ];
                    continue;
                }
                if ($v['value']) {
                    $logisticsServices[] = [
                        'service_code' => $k,
                        'service_value' => json_encode([
                            'value' => $v['value']
                        ])
                    ];
                }
            }
            if (!empty($logisticsServices)) {
                $sendInfo['service']['service_list'] = $logisticsServices;
            }
        } elseif (trim($params['monthly_account']) != 'default') {
            # 根据wap发货单的门店获取发货人信息
            $o2oStoreInfo = $branchObj->getStoreSenderInfoByBranchId($dlyInfo['branch_id']);
            if (empty($o2oStoreInfo['consignee']['name']) || empty($o2oStoreInfo['consignee']['addr'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置发件人信息';
                return $result;
            }

            $sendInfo = [
                'consignee' => [
                    'name' => $o2oStoreInfo['consignee']['name'],
                    'province' => $o2oStoreInfo['consignee']['province'],
                    'city' => $o2oStoreInfo['consignee']['city'],
                    'district' => $o2oStoreInfo['consignee']['district'],
                    'town' => $o2oStoreInfo['consignee']['town'] ?? '',
                    'addr' => $o2oStoreInfo['consignee']['addr'],
                    'telephone' => $o2oStoreInfo['consignee']['telephone'],
                    'mobile' => $o2oStoreInfo['consignee']['mobile'],
                    'zip' => $o2oStoreInfo['consignee']['zip'] ?? '000000',
                ],
                'service' => [
                    'monthly_type' => 'store',  // 月结号类型  village：小镇，store：门店
                    'monthly_account' => $params['monthly_account'],  // 月结账号
                    'pickup_time' => $params['pickup_time'] ?? 'now',  // 上门取件时间
                    'service_list' => [],
                ]
            ];
            # 所有渠道获取电子面单时，发件人名称后面拼接对应-渠道（需要做个校验，如果名称里含渠道名称，不用再增加）
            $channelName = $funcObj->getChannelName($dlyInfo['shop_type'], $sendInfo['consignee']['name']);
            if (!empty($channelName)) {
                $sendInfo['consignee']['name'] = $channelName;
            }
            # 产品类型
            if (!empty($params['product_type'])) {
                $sendInfo['service']['product_type'] = $params['product_type'];
            }
        }

        # 获取请求参数
        $sdf = kernel::single('erpapi_router_request')->set('waplogistics', $channelData)->electron_directData($params['delivery_id'], $sendInfo);
        if ($sdf['rsp'] == 'fail') {
            $result['rsp'] = 'fail';
            $result['msg'] = '获取运单请求参数失败：' . $sdf['msg'];
            return $result;
        }

        # 获取运单号
        $rsp = kernel::single('erpapi_router_request')->set('waplogistics', $channelData)->electron_directRequest($sdf['data']);
        return $rsp;
    }

    /**
     * 微信小程序电子面单
     * @param $params
     * @param $dlyInfo
     * @return mixed
     */
    private function _ecshopxWayBill($params, $dlyInfo)
    {
        $funcObj = kernel::single('logisticsmanager_waybill_func');
        $channelMdl = app::get('logisticsmanager')->model('channel');
        $objChannelExt = app::get('logisticsmanager')->model('channel_extend');
        $wapDlyMdl = app::get('wap')->model('delivery');
        $branchObj = kernel::single('ome_branch');
        $storeObj = kernel::single('o2o_store');
        $result = ['rsp' => 'succ'];

        $channel_type = $funcObj->getShopType($dlyInfo['shop_type']);
        # 获取电子面单来源
        $filter = [
            'channel_type' => $channel_type,
            'status' => 'true',
            'logistics_code' => $params['logi_code']
        ];
        $channelList = $channelMdl->getList('*', $filter);
        if (empty($channelList)) {
            $result['rsp'] = 'fail';
            $result['msg'] = '未配置快递鸟的' . $params['logi_code'] . '电子面单来源信息';
            return $result;
        }

        $channel_list = [];
        # 获取指定门店的电子面单配置
        foreach ($channelList as $item) {
            list(,, $shop_id) = explode('|||', $item['shop_id']);
            if ($shop_id != $dlyInfo['shop_id']) {
                continue;
            }
            $channel_list[] = $item;
        }
        if (empty($channel_list)) {
            $result['rsp'] = 'fail';
            $result['msg'] = '未配置当前小镇的快递鸟' . $params['logi_code'] . '电子面单来源信息';
            return $result;
        } elseif (count($channel_list) > 1) {
            $result['rsp'] = 'fail';
            $result['msg'] = '当前小镇配置的快递鸟' . $params['logi_code'] . '电子面单来源过多，当前条数为：' . count($channel_list);
            return $result;
        }
        # 设置当前门店的电子面单来源信息
        $channelInfo = current($channel_list);

        # 获取门店的发件人信息
        $o2oStoreInfo = $branchObj->getStoreSenderInfoByBranchId($dlyInfo['branch_id']);
        if (empty($o2oStoreInfo['consignee']['name']) || empty($o2oStoreInfo['consignee']['addr'])) {
            $result['rsp'] = 'fail';
            $result['msg'] = '当前门店没有配置发件人信息';
            return $result;
        }

        # 所有渠道获取电子面单时，发件人名称后面拼接对应-渠道（需要做个校验，如果名称里含渠道名称，不用再增加）
        $channelName = $funcObj->getChannelName($dlyInfo['shop_type'], $o2oStoreInfo['consignee']['name']);
        if (!empty($channelName)) {
            $o2oStoreInfo['consignee']['name'] = $channelName;
        }

        # 获取小镇的月结卡号
        $channelExt = $objChannelExt->dump(array('channel_id' => $channelInfo['channel_id']), 'channel_id,monthly_account,addon');
        # 判断是否使用小镇月结账号
        if (empty($params['monthly_account']) || $params['monthly_account'] == 'default') {
            if (empty($channelExt) || empty($channelExt['monthly_account'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '小镇的电子面单来源未配置' . $params['logi_code'] . '月结卡号';
                return $result;
            }

            $monthly_account = $channelExt['monthly_account'];
            $monthly_type = 'village';
        } else {
            # 获取门店的客户编码和月结卡号
            $storeWaybill = $storeObj->getStoreWaybill($o2oStoreInfo['store_id'], $params['logi_code']);
            if (empty($storeWaybill['corp_month_account'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置[' . $params['logi_code'] . ']电子面单月结卡号';
                return $result;
            }

            $monthly_account = $storeWaybill['corp_month_account'];
            $monthly_type = 'store';
        }

        # 根据wms发货单号查订单号
        $sql = "SELECT b.order_id,c.order_bn from sdb_ome_delivery a"
            . " LEFT JOIN sdb_ome_delivery_order b ON a.delivery_id = b.delivery_id"
            . " LEFT JOIN sdb_ome_orders c ON b.order_id = c.order_id"
            . " WHERE a.delivery_bn = '{$dlyInfo['delivery_bn']}'";
        $orderList = kernel::database()->select($sql);
        if (empty($orderList)) {
            $result['rsp'] = 'fail';
            $result['msg'] = '发货单对应的订单列表为空';
            return $result;
        }

        # wap发货单信息
        $wapDeliveryInfo = $wapDlyMdl->dump(array('delivery_id' => $params['delivery_id']), '*');

        # 发货人参数
        $sendInfo = [
            'default_sender' => $o2oStoreInfo['consignee']['name'],
            'province' => $o2oStoreInfo['consignee']['province'],
            'city' => $o2oStoreInfo['consignee']['city'],
            'area' => $o2oStoreInfo['consignee']['district'],
            'street' => $o2oStoreInfo['consignee']['town'] ?? '',
            'address_detail' => $o2oStoreInfo['consignee']['addr'],
            'tel' => $o2oStoreInfo['consignee']['telephone'],
            'mobile' => $o2oStoreInfo['consignee']['mobile'],
            'zip' => $o2oStoreInfo['consignee']['zip'] ?? '000000',
            'shop_name' => $o2oStoreInfo['store_name'],
            'service' => [
                'monthly_type' => $monthly_type, // 月结号类型  village：小镇，store：门店
                'monthly_account' => $monthly_account,  // 月结账号
                'product_type' => $params['product_type'], // 服务类型
                'pickup_time' => $params['pickup_time'] ?? 'now', // 上门取件时间
            ],
        ];

        # 所有渠道获取电子面单时，发件人名称后面拼接对应-渠道（需要做个校验，如果名称里含渠道名称，不用再增加）
        $channelName = $funcObj->getChannelName($dlyInfo['shop_type'], $sendInfo['default_sender']);
        if (!empty($channelName)) {
            $sendInfo['default_sender'] = $channelName;
        }

        $arrDeliveryId = [];
        $deliveryItem = $wapDeliveryInfo;
        $deliveryItem['order_bns'] = array_column($orderList, 'order_bn');
        $arrDeliveryId[] = $deliveryItem;
        # 获取电子面单参数
        $logistics_data = kernel::single('wap_event_trigger_logistics_data_electron_router')
            ->setChannel($channelInfo)
            ->getDirectSdf($arrDeliveryId, [], $sendInfo);
        # 京东的app_key相关参数
        $logi_code = strtoupper($params['logi_code']);
        if ($logi_code == 'JD') {
            # 快递鸟京东标识符
            $logistics_data['version'] = 'jdl';
            $logistics_data['LogisticsRouteCode'] = 'OPEN'; // 下单平台 COLD-冷链,OPEN-京东开发平台,JOS-京东宙斯
            # 读取密钥信息
            if (!empty($channelExt['addon'])) {
                $extend = [
                    'app_key' => $channelExt['addon']['app_key'],
                    'app_secret' => $channelExt['addon']['app_secret'],
                    'access_token' => $channelExt['addon']['access_token'],
                ];
                $logistics_data['extend'] = array_merge($logistics_data['extend'], $extend);
            }
        }

        # 通知上面取件 0：通知，1：不通知，默认：0
        $logistics_data['extend']['is_notice'] = 0;
        # 读取快递鸟配置
        $hqepay_config = kernel::single('logisticsmanager_waybill_func')->corpCode2ChannelService();
        $notice_config = $hqepay_config[$logi_code]['hqepay_isnotice'] ?? [];
        if (!empty($notice_config) && isset($notice_config[$params['product_type']])) {
            $logistics_data['extend']['is_notice'] = $notice_config[$params['product_type']];
        }

        # 获取电子面单
        $rsp = kernel::single('erpapi_router_request')->set('logistics', $channelInfo['channel_id'])->electron_directRequest($logistics_data);
        return $rsp;
    }

    /**
     * 微信视频号电子面单
     * @param $params
     * @param $dlyInfo
     * @return array
     */
    private function _wxshipinWayBill($params, $dlyInfo)
    {
        $funcObj = kernel::single('logisticsmanager_waybill_func');
        $channelMdl = app::get('logisticsmanager')->model('channel');
        $objChannelExt = app::get('logisticsmanager')->model('channel_extend');
        $wapDlyMdl = app::get('wap')->model('delivery');
        $branchObj = kernel::single('ome_branch');
        $storeObj = kernel::single('o2o_store');
        $result = ['rsp' => 'succ'];

        # 判断是否使用小镇月结账号
        if (empty($params['monthly_account']) || $params['monthly_account'] == 'default') {
            $channel_type = $funcObj->getShopType($dlyInfo['shop_type']);
            # 获取电子面单来源
            $filter = [
                'channel_type' => $channel_type,
                'shop_id' => $dlyInfo['shop_id'],
                'status' => 'true',
                'logistics_code' => $params['logi_code']
            ];
            $channelInfo = $channelMdl->dump($filter, '*');
            if (empty($channelInfo)) {
                $result['rsp'] = 'fail';
                $result['msg'] = '小镇未配置' . $params['logi_code'] . '电子面单来源信息';
                return $result;
            }

            # 获取小镇的月结卡号
            $channelExt = $objChannelExt->dump(array('channel_id' => $channelInfo['channel_id']), 'channel_id,monthly_account,addon');
            if (empty($channelExt) || empty($channelExt['monthly_account'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '小镇的电子面单来源未配置' . $params['logi_code'] . '电子面单月结卡号';
                return $result;
            }

            # 获取门店的发件人信息
            $o2oStoreInfo = $branchObj->getStoreSenderInfoByBranchId($dlyInfo['branch_id']);
            if (empty($o2oStoreInfo['consignee']['name']) || empty($o2oStoreInfo['consignee']['addr'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置发件人信息';
                return $result;
            }

            # 发件人信息
            $sendInfo = [
                'default_sender' => $o2oStoreInfo['consignee']['name'],
                'province' => $o2oStoreInfo['consignee']['province'],
                'city' => $o2oStoreInfo['consignee']['city'],
                'area' => $o2oStoreInfo['consignee']['district'],
                'street' => $o2oStoreInfo['consignee']['town'] ?? '',
                'address_detail' => $o2oStoreInfo['consignee']['addr'],
                'tel' => $o2oStoreInfo['consignee']['telephone'],
                'mobile' => $o2oStoreInfo['consignee']['mobile'],
                'zip' => $o2oStoreInfo['consignee']['zip'] ?? '000000',
                'shop_name' => $o2oStoreInfo['store_name'],
                'addon' => $channelExt['addon'],
                'service' => [
                    'monthly_type' => 'village',  // 月结号类型  village：小镇，store：门店
                    'customer_code' => $channelExt['addon']['acct_id'], // 客户编码
                    'monthly_account' => $channelExt['monthly_account'],  // 月结账号
                    'product_type' => $params['product_type'], // 服务类型
                    'pickup_time' => $params['pickup_time'] ?? 'now', // 上门取件时间
                ],
            ];

            # 所有渠道获取电子面单时，发件人名称后面拼接对应-渠道（需要做个校验，如果名称里含渠道名称，不用再增加）
            $channelName = $funcObj->getChannelName($dlyInfo['shop_type'], $sendInfo['default_sender']);
            if (!empty($channelName)) {
                $sendInfo['default_sender'] = $channelName;
            }

            # 根据wms发货单号查订单号
            $sql = "SELECT b.order_id,c.order_bn from sdb_ome_delivery a"
                . " LEFT JOIN sdb_ome_delivery_order b ON a.delivery_id = b.delivery_id"
                . " LEFT JOIN sdb_ome_orders c ON b.order_id = c.order_id"
                . " WHERE a.delivery_bn = '{$dlyInfo['delivery_bn']}'";
            $orderList = kernel::database()->select($sql);
            if (empty($orderList)) {
                $result['rsp'] = 'fail';
                $result['msg'] = '发货单对应的订单列表为空';
                return $result;
            }

            # wap发货单信息
            $wapDeliveryInfo = $wapDlyMdl->dump(array('delivery_id' => $params['delivery_id']), '*');

            $arrDeliveryId = [];
            $deliveryItem = $wapDeliveryInfo;
            $deliveryItem['order_bns'] = array_column($orderList, 'order_bn');
            $arrDeliveryId[] = $deliveryItem;
            # 获取电子面单参数
            $logistics_data = kernel::single('wap_event_trigger_logistics_data_electron_router')
                ->setChannel($channelInfo)
                ->getDirectSdf($arrDeliveryId, [], $sendInfo);

            $channel = [
                'shop_id' => $dlyInfo['shop_id'],
                'company_code' => $channelInfo['logistics_code'],
            ];
        } elseif (trim($params['monthly_account']) != 'default') {
            # 根据wap发货单的门店获取发货人信息
            $o2oStoreInfo = $branchObj->getStoreSenderInfoByBranchId($dlyInfo['branch_id']);
            if (empty($o2oStoreInfo['consignee']['name']) || empty($o2oStoreInfo['consignee']['addr'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置发件人信息';
                return $result;
            }

            # 获取门店的客户编码和月结卡号
            $storeWaybill = $storeObj->getStoreWaybill($o2oStoreInfo['store_id'], $params['logi_code']);
            if (empty($storeWaybill) || empty($storeWaybill['corp_month_customer'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置[' . $params['logi_code'] . ']电子面单客户编码信息';
                return $result;
            } elseif (empty($storeWaybill['corp_month_account'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置[' . $params['logi_code'] . ']电子面单月结卡号信息';
                return $result;
            }

            $channelInfo = [
                'channel_type' => $funcObj->getShopType($dlyInfo['shop_type']),
            ];

            # 根据wms发货单号查订单号
            $sql = "SELECT b.order_id,c.order_bn from sdb_ome_delivery a"
                . " LEFT JOIN sdb_ome_delivery_order b ON a.delivery_id = b.delivery_id"
                . " LEFT JOIN sdb_ome_orders c ON b.order_id = c.order_id"
                . " WHERE a.delivery_bn = '{$dlyInfo['delivery_bn']}'";
            $orderList = kernel::database()->select($sql);
            if (empty($orderList)) {
                $result['rsp'] = 'fail';
                $result['msg'] = '发货单对应的订单列表为空';
                return $result;
            }

            # wap发货单信息
            $wapDeliveryInfo = $wapDlyMdl->dump(array('delivery_id' => $params['delivery_id']), '*');

            $arrDeliveryId = [];
            $deliveryItem = $wapDeliveryInfo;
            $deliveryItem['order_bns'] = array_column($orderList, 'order_bn');
            $arrDeliveryId[] = $deliveryItem;

            # 发货人参数
            $sendInfo = [
                'default_sender' => $o2oStoreInfo['consignee']['name'],
                'province' => $o2oStoreInfo['consignee']['province'],
                'city' => $o2oStoreInfo['consignee']['city'],
                'area' => $o2oStoreInfo['consignee']['district'],
                'street' => $o2oStoreInfo['consignee']['town'] ?? '',
                'address_detail' => $o2oStoreInfo['consignee']['addr'],
                'tel' => $o2oStoreInfo['consignee']['telephone'],
                'mobile' => $o2oStoreInfo['consignee']['mobile'],
                'zip' => $o2oStoreInfo['consignee']['zip'] ?? '000000',
                'shop_name' => $o2oStoreInfo['store_name'],
                'service' => [
                    'monthly_type' => 'store',  // 月结号类型  village：小镇，store：门店
                    'customer_code' => $storeWaybill['corp_month_customer'], // 客户编码
                    'monthly_account' => $storeWaybill['corp_month_account'],  // 月结账号
                    'product_type' => $params['product_type'], // 服务类型
                    'pickup_time' => $params['pickup_time'] ?? 'now', // 上门取件时间
                ],
            ];

            # 所有渠道获取电子面单时，发件人名称后面拼接对应-渠道（需要做个校验，如果名称里含渠道名称，不用再增加）
            $channelName = $funcObj->getChannelName($dlyInfo['shop_type'], $sendInfo['default_sender']);
            if (!empty($channelName)) {
                $sendInfo['default_sender'] = $channelName;
            }

            # 获取电子面单参数
            $logistics_data = kernel::single('wap_event_trigger_logistics_data_electron_router')
                ->setChannel($channelInfo)
                ->getDirectSdf($arrDeliveryId, [], $sendInfo);

            # 根据shop_id获取小镇电子面单来源的店铺ID字段
            $sql = "select b.addon from sdb_logisticsmanager_channel a"
                . " LEFT JOIN sdb_logisticsmanager_channel_extend b ON a.channel_id =b.channel_id"
                . " WHERE a.shop_id = '" . $dlyInfo['shop_id']
                . "' and a.logistics_code = '" . $params['logi_code'] . "' and a.status = 'true'";
            $extendList = kernel::database()->select($sql);
            if (!empty($extendList)) {
                $extendAddon = unserialize($extendList[0]['addon']);
                # 店铺id（从查询开通账号信息接口获取）
                if (!empty($extendAddon) && !empty($extendAddon['shop_id'])) {
                    $logistics_data['extend']['shop_id'] = $extendAddon['shop_id'];
                }
            }

            $channel = [
                'shop_id' => $dlyInfo['shop_id'],
                'company_code' => $params['logi_code'],
            ];
        }

        # 获取电子面单
        $rsp = kernel::single('erpapi_router_request')->set('waplogistics', $channel)->electron_directRequest($logistics_data);
        return $rsp;
    }

    /**
     * 电子面单打印
     * @param $delivery_id
     * @return mixed
     */
    public function print($delivery_id)
    {
        $orderMdl = app::get('ome')->model('orders');
        $wapDlyMdl = app::get('wap')->model('delivery');
        $omsDlyMdl = app::get('ome')->model('delivery');

        # 检查发货单状态
        $result = ['rsp' => 'succ'];
        # 检查发货单状态
        $msg = '';
        $omsDlyInfo = kernel::single("wap_delivery")->checkDeliveryStatus($delivery_id, $msg);
        if (!$omsDlyInfo) {
            $result['res'] = 'fail';
            $result['msg'] = $msg;
            return $result;
        }

        # 店铺类型转换
        if ($omsDlyInfo['shop_type'] == 'ecos.ecshopx') {
            $shop_type = str_replace('.', '_', $omsDlyInfo['shop_type']);
        } else {
            $shop_type = $omsDlyInfo['shop_type'];
        }

        $class = sprintf('logistics_waybill_print_%s', $shop_type);
        # 参数
        $params = [
            'delivery' => $omsDlyInfo,
            'wap_delivery_id' => $delivery_id,
            'wap_delivery_bn' => $omsDlyInfo['wap_delivery_bn'],
            'waybill_number' => $omsDlyInfo['logi_no'],
            'shop_type' => $omsDlyInfo['shop_type'],
            'branch_id' => $omsDlyInfo['branch_id']
        ];
        $printRes = kernel::single($class)->doPrint($params);
        if ($printRes['rsp'] != 'succ') {
            $result['rsp'] = 'fail';
            $result['msg'] = $printRes['msg'];
            return $result;
        }

        # 更新发货单的打印状态
        $updateDly = [
            'print_status' => 7
        ];
        $wapDlyMdl->update($updateDly, ['delivery_id' => $delivery_id]);
        $omsDlyMdl->update($updateDly, ['delivery_id' => $omsDlyInfo['delivery_id']]);
        # 更新订单的打印状态
        $updateOrder = [
            'print_finish' => 'true',
            'print_status' => 7
        ];
        foreach ($printRes['data']['order_id'] as $order_id) {
            $orderMdl->update($updateOrder, ['order_id' => $order_id]);
        }
        return $result;
    }

    /**
     * 取消电子面单
     * @param $delivery_id
     * @return mixed
     */
    public function recycleWaybill($delivery_id, $interception_id = null)
    {
        # 检查发货单状态
        $result = ['rsp' => 'succ'];
        if (empty($delivery_id)) {
            $result['rsp'] = 'fail';
            $result['msg'] = '发货单ID参数不能为空';
            return $result;
        }

        $waybillModel = app::get('logisticsmanager')->model('waybill');
        $deliveryMdl = app::get('wap')->model('delivery');
        $omeDeliveryObj = app::get('ome')->model('delivery');
        $funcObj = kernel::single('logisticsmanager_waybill_func');

        $dly_filter = [
            'delivery_id' => $delivery_id
        ];
        # 获取发货单信息
        $deliveryInfo = $deliveryMdl->dump($dly_filter, 'delivery_id,delivery_bn,outer_delivery_bn');
        if (empty($deliveryInfo)) {
            $result['rsp'] = 'fail';
            $result['msg'] = 'h5发货单不存在';
            return $result;
        }

        # 发货单号
        $delivery_bn = empty($deliveryInfo['outer_delivery_bn']) ? $deliveryInfo['delivery_bn'] : $deliveryInfo['outer_delivery_bn'];
        # oms的发货单
        $omeDeliveryInfo = $omeDeliveryObj->dump(array('delivery_bn' => $delivery_bn), 'delivery_id,shop_id,shop_type');
        if (empty($omeDeliveryInfo)) {
            $result['rsp'] = 'fail';
            $result['msg'] = 'oms发货单不存在';
            return $result;
        }

        # 获取电子面单信息
        # 物流拦截过来的取消不用这个条件
        if (empty($interception_id) || !$interception_id) {
            $dly_filter['status'] = '1';
        }

        $waybillInfo = $waybillModel->dump($dly_filter, 'id,waybill_number,channel_id,logistics_code,monthly_account,unique_code');
        if (empty($waybillInfo)) {
            $result['rsp'] = 'fail';
            $result['msg'] = '电子面单信息不存在或已经取消';
            return $result;
        }

        # 物流公司编码转换
        if (!empty($waybillInfo['logistics_code'])) {
            $waybillInfo['logistics_code'] = $funcObj->getLogisticsCode($omeDeliveryInfo['shop_type'], $waybillInfo['logistics_code']);
        }

        $channelData = [
            'shop_id' => $omeDeliveryInfo['shop_id'],
            'company_code' => $waybillInfo['logistics_code'],
            'waybill_number' => $waybillInfo['waybill_number'],
            'channel_id' => $waybillInfo['channel_id'],
            'waybill_id' => $waybillInfo['id'],
            'delivery_id' => $delivery_id,
            'delivery_bn' => $delivery_bn,
            'monthly_account' => $waybillInfo['monthly_account'],
            'unique_code' => $waybillInfo['unique_code'] ?? '',
        ];
        # 物流拦截
        if (!empty($interception_id)) {
            $channelData['interception_id'] = $interception_id;
        }
        # 小程序走快递鸟通道
        if ($omeDeliveryInfo['shop_type'] == 'ecos.ecshopx') {
            $result = kernel::single('erpapi_router_request')->set('logistics', $channelData['channel_id'])->electron_recycleWaybillNew($channelData);
        } else {
            $result = kernel::single('erpapi_router_request')->set('waplogistics', $channelData)->electron_recycleWaybillNew($channelData);
        }
        return $result;
    }

    /**
     * 获取电子面单的发货人信息
     * @param $delivery_id
     * @param $shop_type
     * @return mixed
     */
    public function getSender($delivery_id, $shop_type)
    {
        if (empty($delivery_id) || empty($shop_type)) {
            return false;
        }

        $waybillModel = app::get('logisticsmanager')->model('waybill');

        # 获取电子面单信息
        $dly_filter = [
            'delivery_id' => $delivery_id
        ];
        $waybillInfo = $waybillModel->dump($dly_filter, 'id,send_info');
        if (empty($waybillInfo) || empty($waybillInfo['send_info'])) {
            return false;
        }

        # 发货人信息
        $senderInfo = unserialize($waybillInfo['send_info']);
        if (empty($senderInfo)) {
            return false;
        }

        $result = [];
        switch ($shop_type) {
            case 'wxshipin':
                $result['name'] = $senderInfo['name'];
                $result['phone'] = $senderInfo['mobile'];
                $result['province'] = $senderInfo['province'];
                $result['city'] = $senderInfo['city'];
                $result['area'] = $senderInfo['county'];
                $result['street'] = $senderInfo['street'] ?? '';
                $result['address'] = $senderInfo['address'];
                break;
            case 'luban':
                $result['name'] = $senderInfo['contact']['name'];
                $result['phone'] = $senderInfo['contact']['phone'];
                $result['province'] = $senderInfo['contact']['address']['province_name'];
                $result['city'] = $senderInfo['contact']['address']['city_name'];
                $result['area'] = $senderInfo['contact']['address']['district_name'];
                $result['street'] = $senderInfo['contact']['address']['street_name'] ?? '';
                $result['address'] = $senderInfo['contact']['address']['detail_address'];
                break;
            case 'ecos.ecshopx':
                $result['name'] = $senderInfo['default_sender'];
                $result['phone'] = $senderInfo['mobile'];
                $result['province'] = $senderInfo['province'];
                $result['city'] = $senderInfo['city'];
                $result['area'] = $senderInfo['area'];
                $result['street'] = $senderInfo['street'] ?? '';
                $result['address'] = $senderInfo['address_detail'];
                break;
        }
        return $result;
    }

    /**
     * 小红书电子面单
     * @param $params
     * @param $dlyInfo
     * @return mixed
     */
    private function _xhsWayBill($params, $dlyInfo)
    {
        $funcObj = kernel::single('logisticsmanager_waybill_func');
        $channelMdl = app::get('logisticsmanager')->model('channel');
        $objChannelExt = app::get('logisticsmanager')->model('channel_extend');
        $wapDlyMdl = app::get('wap')->model('delivery');
        $branchObj = kernel::single('ome_branch');
        $storeObj = kernel::single('o2o_store');
        $result = ['rsp' => 'succ'];

        # 判断是否使用小镇月结账号
        if (empty($params['monthly_account']) || $params['monthly_account'] == 'default') {
            $channel_type = $funcObj->getShopType($dlyInfo['shop_type']);
            # 获取电子面单来源
            $filter = [
                'channel_type' => $channel_type,
                'shop_id' => $dlyInfo['shop_id'],
                'status' => 'true',
                'logistics_code' => $params['logi_code']
            ];
            $channelInfo = $channelMdl->dump($filter, '*');
            if (empty($channelInfo)) {
                $result['rsp'] = 'fail';
                $result['msg'] = '小镇未配置' . $params['logi_code'] . '电子面单来源信息';
                return $result;
            }

            # 获取小镇的月结卡号
            $channelExt = $objChannelExt->dump(array('channel_id' => $channelInfo['channel_id']), 'channel_id,monthly_account,addon');
            if (empty($channelExt) || empty($channelExt['monthly_account'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '小镇的电子面单来源未配置' . $params['logi_code'] . '电子面单月结卡号';
                return $result;
            }

            # 获取门店的发件人信息
            $o2oStoreInfo = $branchObj->getStoreSenderInfoByBranchId($dlyInfo['branch_id']);
            if (empty($o2oStoreInfo['consignee']['name']) || empty($o2oStoreInfo['consignee']['addr'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置发件人信息';
                return $result;
            }

            # 发件人信息
            $sendInfo = [
                'default_sender' => $o2oStoreInfo['consignee']['name'],
                'province' => $o2oStoreInfo['consignee']['province'],
                'city' => $o2oStoreInfo['consignee']['city'],
                'area' => $o2oStoreInfo['consignee']['district'],
                'street' => $o2oStoreInfo['consignee']['town'] ?? '',
                'address_detail' => $o2oStoreInfo['consignee']['addr'],
                'tel' => $o2oStoreInfo['consignee']['telephone'],
                'mobile' => $o2oStoreInfo['consignee']['mobile'],
                'zip' => $o2oStoreInfo['consignee']['zip'] ?? '000000',
                'shop_name' => $o2oStoreInfo['store_name'],
                'addon' => $channelExt['addon'],
                'service' => [
                    'monthly_type' => 'village',  // 月结号类型  village：小镇，store：门店
                    'customer_code' => $channelExt['addon']['acct_id'], // 客户编码
                    'monthly_account' => $channelExt['monthly_account'],  // 月结账号
                    'product_type' => $params['product_type'], // 服务类型
                    'pickup_time' => $params['pickup_time'] ?? 'now', // 上门取件时间
                ],
            ];

            # 所有渠道获取电子面单时，发件人名称后面拼接对应-渠道（需要做个校验，如果名称里含渠道名称，不用再增加）
            $channelName = $funcObj->getChannelName($dlyInfo['shop_type'], $sendInfo['default_sender']);
            if (!empty($channelName)) {
                $sendInfo['default_sender'] = $channelName;
            }

            # 根据wms发货单号查订单号
            $sql = "SELECT b.order_id,c.order_bn from sdb_ome_delivery a"
                . " LEFT JOIN sdb_ome_delivery_order b ON a.delivery_id = b.delivery_id"
                . " LEFT JOIN sdb_ome_orders c ON b.order_id = c.order_id"
                . " WHERE a.delivery_bn = '{$dlyInfo['delivery_bn']}'";
            $orderList = kernel::database()->select($sql);
            if (empty($orderList)) {
                $result['rsp'] = 'fail';
                $result['msg'] = '发货单对应的订单列表为空';
                return $result;
            }

            # wap发货单信息
            $wapDeliveryInfo = $wapDlyMdl->dump(array('delivery_id' => $params['delivery_id']), '*');

            $arrDeliveryId = [];
            $deliveryItem = $wapDeliveryInfo;
            $deliveryItem['order_bns'] = array_column($orderList, 'order_bn');
            $arrDeliveryId[] = $deliveryItem;
            # 获取电子面单参数
            $logistics_data = kernel::single('wap_event_trigger_logistics_data_electron_router')
                ->setChannel($channelInfo)
                ->getDirectSdf($arrDeliveryId, [], $sendInfo);

            $channel = [
                'shop_id' => $dlyInfo['shop_id'],
                'company_code' => $channelInfo['logistics_code'],
            ];
        } elseif (trim($params['monthly_account']) != 'default') {
            # 根据wap发货单的门店获取发货人信息
            $o2oStoreInfo = $branchObj->getStoreSenderInfoByBranchId($dlyInfo['branch_id']);
            if (empty($o2oStoreInfo['consignee']['name']) || empty($o2oStoreInfo['consignee']['addr'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置发件人信息';
                return $result;
            }

            # 获取门店的客户编码和月结卡号
            $storeWaybill = $storeObj->getStoreWaybill($o2oStoreInfo['store_id'], $params['logi_code']);
            if (empty($storeWaybill) || empty($storeWaybill['corp_month_customer'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置[' . $params['logi_code'] . ']电子面单客户编码信息';
                return $result;
            } elseif (empty($storeWaybill['corp_month_account'])) {
                $result['rsp'] = 'fail';
                $result['msg'] = '当前门店没有配置[' . $params['logi_code'] . ']电子面单月结卡号信息';
                return $result;
            }

            $channelInfo = [
                'channel_type' => $funcObj->getShopType($dlyInfo['shop_type']),
            ];

            # 根据wms发货单号查订单号
            $sql = "SELECT b.order_id,c.order_bn from sdb_ome_delivery a"
                . " LEFT JOIN sdb_ome_delivery_order b ON a.delivery_id = b.delivery_id"
                . " LEFT JOIN sdb_ome_orders c ON b.order_id = c.order_id"
                . " WHERE a.delivery_bn = '{$dlyInfo['delivery_bn']}'";
            $orderList = kernel::database()->select($sql);
            if (empty($orderList)) {
                $result['rsp'] = 'fail';
                $result['msg'] = '发货单对应的订单列表为空';
                return $result;
            }

            # wap发货单信息
            $wapDeliveryInfo = $wapDlyMdl->dump(array('delivery_id' => $params['delivery_id']), '*');

            $arrDeliveryId = [];
            $deliveryItem = $wapDeliveryInfo;
            $deliveryItem['order_bns'] = array_column($orderList, 'order_bn');
            $arrDeliveryId[] = $deliveryItem;

            # 发货人参数
            $sendInfo = [
                'default_sender' => $o2oStoreInfo['consignee']['name'],
                'province' => $o2oStoreInfo['consignee']['province'],
                'city' => $o2oStoreInfo['consignee']['city'],
                'area' => $o2oStoreInfo['consignee']['district'],
                'street' => $o2oStoreInfo['consignee']['town'] ?? '',
                'address_detail' => $o2oStoreInfo['consignee']['addr'],
                'tel' => $o2oStoreInfo['consignee']['telephone'],
                'mobile' => $o2oStoreInfo['consignee']['mobile'],
                'zip' => $o2oStoreInfo['consignee']['zip'] ?? '000000',
                'shop_name' => $o2oStoreInfo['store_name'],
                'service' => [
                    'monthly_type' => 'store',  // 月结号类型  village：小镇，store：门店
                    'customer_code' => $storeWaybill['corp_month_customer'], // 客户编码
                    'monthly_account' => $storeWaybill['corp_month_account'],  // 月结账号
                    'product_type' => $params['product_type'], // 服务类型
                    'pickup_time' => $params['pickup_time'] ?? 'now', // 上门取件时间
                ],
            ];

            # 所有渠道获取电子面单时，发件人名称后面拼接对应-渠道（需要做个校验，如果名称里含渠道名称，不用再增加）
            $channelName = $funcObj->getChannelName($dlyInfo['shop_type'], $sendInfo['default_sender']);
            if (!empty($channelName)) {
                $sendInfo['default_sender'] = $channelName;
            }

            # 获取电子面单参数
            $logistics_data = kernel::single('wap_event_trigger_logistics_data_electron_router')
                ->setChannel($channelInfo)
                ->getDirectSdf($arrDeliveryId, [], $sendInfo);

            # 根据shop_id获取小镇电子面单来源的店铺ID字段
            $sql = "select b.addon from sdb_logisticsmanager_channel a"
                . " LEFT JOIN sdb_logisticsmanager_channel_extend b ON a.channel_id =b.channel_id"
                . " WHERE a.shop_id = '" . $dlyInfo['shop_id']
                . "' and a.logistics_code = '" . $params['logi_code'] . "' and a.status = 'true'";
            $extendList = kernel::database()->select($sql);
            if (!empty($extendList)) {
                $extendAddon = unserialize($extendList[0]['addon']);
                # 店铺id（从查询开通账号信息接口获取）
                if (!empty($extendAddon) && !empty($extendAddon['shop_id'])) {
                    $logistics_data['extend']['shop_id'] = $extendAddon['shop_id'];
                }
            }

            $channel = [
                'shop_id' => $dlyInfo['shop_id'],
                'company_code' => $params['logi_code'],
            ];
        }

        # 获取电子面单
        $rsp = kernel::single('erpapi_router_request')->set('waplogistics', $channel)->electron_directRequest($logistics_data);
        return $rsp;
    }
}
