<?php
/**
 * ============================
 * @Author:   zhouhui   
 * @Version:  1.0
 * @DateTime: 2025/07/30 15:49:16
 * @describe: 小红书
 * ============================
 */

class wap_event_trigger_logistics_data_electron_xhs extends wms_event_trigger_logistics_data_electron_common
{

    public function getDirectSdf($arrDelivery, $arrBill, $shop)
    {
        $delivery = $arrDelivery[0];
        if (empty($arrBill)) {
            $this->needRequestId[] = $delivery['delivery_id'];
        } else {
            $this->needRequestId[] = $arrBill[0]['log_id'];
            $delivery['delivery_bn'] = $this->setChildRqOrdNo($delivery['delivery_bn'], $arrBill[0]['log_id']);
        }

        # 发货单商品明细
        $deliveryItems = $this->getDeliveryItems($delivery['delivery_id']);
        # 扩展参数
        if (!empty($shop['addon'])) {
            foreach ($shop['addon'] as $k => $v) {
                $shop[$k] = $v;
            }
        }

        $ordersMdl = app::get('ome')->model('orders');
        $orderItemsMdl = app::get('ome')->model('order_items');
        $orderObj = kernel::single('ome_order');

        # 订单列表
        $orders = $ordersMdl->getList('total_amount,shop_type,order_bn,custom_mark,mark_text,order_id,relate_order_bn', array('order_bn|in' => $delivery['order_bns']));
        if (empty($orders)) {
            return [];
        }

        $orderItemsList = $orderItemsMdl->getList('*', ['order_id|in' => array_column($orders, 'order_id')]);
        $shopGoodsIdList = array_column($orderItemsList, 'shop_goods_id', 'shop_product_id');

        foreach ($deliveryItems as $k => $v) {
            if ($shopGoodsIdList[$v['shop_product_id']]) {
                $deliveryItems[$k]['shop_goods_id'] = $shopGoodsIdList[$v['shop_product_id']];
            }
        }

        $total_amount = 0;
        foreach ($orders as $k => $order) {
            $total_amount += $order['total_amount'];
            $shop['shop_type'] = $order['shop_type'];

            # 获取当前订单的原始订单号
            $orders[$k]['source_order_bn'] = $orderObj->getSourceOrder($order);
        }

        $dlyCorp = app::get('ome')->model('dly_corp')->dump(array('corp_id' => $delivery['logi_id']));

        $sdf = [];
        $sdf['primary_bn'] = $delivery['delivery_bn'];
        $sdf['monthly_type'] = $shop['service']['monthly_type'];       // 月结号类型  village：小镇，store：门店
        $sdf['customer_code'] = $shop['service']['customer_code'];     // 客户编码
        $sdf['monthly_account'] = $shop['service']['monthly_account']; // 月结账号
        $sdf['product_type'] = $shop['service']['product_type'];       // 产品类型
        $sdf['pickup_time'] = $shop['service']['pickup_time'];         // 上门取件时间
        $sdf['delivery'] = $delivery;
        $sdf['delivery_item'] = $deliveryItems;
        $sdf['shop'] = $shop;
        $sdf['dly_corp'] = $dlyCorp;
        $sdf['total_amount'] = $total_amount;
        $sdf['order'] = $orders;
        $sdf['order_bns'] = $delivery['order_bns'];
        # 小镇发货时使用的电子面单来源id，门店发货没有该参数
        if (!empty($this->channel['channel_id'])) {
            $sdf['channel_id'] = $this->channel['channel_id'];
        }
        $sdf['extend'] = []; // 其他扩展参数
        return $sdf;
    }

    public function getDeliveryItems($delivery_id)
    {
        static $deliveryItems = array();
        if (!$deliveryItems[$delivery_id]) {
            $deliveryItems[$delivery_id] = app::get('wap')->model('delivery_items')->getList('*', array('delivery_id' => $delivery_id));
        }
        return $deliveryItems[$delivery_id];
    }

}
