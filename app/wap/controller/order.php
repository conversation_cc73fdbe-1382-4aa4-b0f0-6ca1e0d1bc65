<?php

use PHPUnit\Util\Json;

/**
 * 订单中心
 */
class wap_ctl_order extends wap_controller
{
    var $delivery_link    = array();

    function __construct($app)
    {
        parent::__construct($app);

        //地图
        $this->delivery_link['mapLink']    = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'showMapByBaidu'), true);

        //确认拒单
        $this->delivery_link['doRefuse']   = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'doRefuse'), true);

        //立即接单
        $this->delivery_link['doConfirm']   = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'doConfirm'), true);

        //发货
        $this->delivery_link['doConsign']    = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'doConsign'), true);

        //签收
        $this->delivery_link['signPage']    = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'signPage'), true);
        $this->delivery_link['doSign']         = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'doSign'), true);
        $this->delivery_link['sendMsg']        = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'sendMsg'), true);
        $this->delivery_link['showOrderInfo']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'showOrderInfo'), true);

        #打印
        $this->delivery_link['doPrint']  = app::get('wap')->router()->gen_url(array('ctl'=>'logistics','act'=>'doPrint'), true);
        # 查看物流
        $this->delivery_link['showLogistics']  = app::get('wap')->router()->gen_url(array('ctl'=>'logistics','act'=>'showLogistics'), true);
        # 补录快递单号
        $this->delivery_link['doAddLogiNo']  = app::get('wap')->router()->gen_url(array('ctl'=>'logistics','act'=>'doAddLogiNo'), true);

         # 批量呼叫快递
        $this->delivery_link['batchOnlineDelivery']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'batchOnlineDelivery'), true);
        $this->delivery_link['getPendingDeliveryOrders']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'getPendingDeliveryOrders'), true);

        # 呼叫快递
        $this->delivery_link['onlineDelivery']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'onlineDelivery'), true);
        # 取消发货
        $this->delivery_link['doCancelDelivery']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'doCancelDelivery'), true);

        # 缺货
        $this->delivery_link['outOfStock']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'outOfStock'), true);
        $this->delivery_link['doOutOfStock']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'doOutOfStock'), true);

        # 批量呼叫快递
        $this->delivery_link['batchOnlineDelivery']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'batchOnlineDelivery'), true);
        $this->delivery_link['getPendingDeliveryOrders']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'getPendingDeliveryOrders'), true);

        //待发货
        $this->delivery_link['orderWaitDelivery']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'index?view=wait_delivery'), true);
        // 待揽件
        $this->delivery_link['orderWaitPickup']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'index?view=wait_pickup'), true);
        // 已签收
        $this->delivery_link['orderDelivered']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'index?view=delivered'), true);
        //已揽件
        $this->delivery_link['orderPicked']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'index?view=picked'), true);
        //已取消
        $this->delivery_link['orderCancel']  = app::get('wap')->router()->gen_url(array('ctl'=>'order','act'=>'index?view=cancel'), true);
        //修改快递单号
        $this->delivery_link['doUpdateLogiNo']  = app::get('wap')->router()->gen_url(array('ctl'=>'logistics','act'=>'doUpdateLogiNo'), true);

        $this->pagedata['delivery_link']   = $this->delivery_link;
    }

    /**
     * 条件
     *
     * status 0:未处理  1:打回  2:暂停 3:已发货
     */
    function _views_confirm($curr_view)
    {
        $base_filter = array();
        $wapDeliveryObj    = app::get('wap')->model('delivery');

        $page              = intval($_POST['page']) ? intval($_POST['page']) : 0;
        $limit             = 10;//每页显示数量
        $offset            = $limit * $page;

        $is_super    = kernel::single('desktop_user')->is_super();
        if(!$is_super)
        {
            $branchObj     = kernel::single('o2o_store_branch');
            $branch_ids    = $branchObj->getO2OBranchByUser(true);
            if(empty($branch_ids))
            {
                $this->pagedata['link_url']     = $this->delivery_link['order_index'];
                $this->pagedata['error_msg']    = '操作员没有管辖的仓库';
                echo $this->fetch('auth_error.html');
                exit;
            }
            $base_filter['branch_id']    = $branch_ids;
        }

        $dly_overtime    = 0;

        //超时订单
        if($curr_view == 'overtime')
        {

            //履约超时时间设置(分钟)
            $minute    = app::get('o2o')->getConf('o2o.delivery.dly_overtime');
            $minute    = intval($minute);

            if($minute)
            {
                $second          = $minute * 60;
                $dly_overtime    = time() - $second;//现在时间 减去 履约时间
            }
        }

        //menu
        $sub_menu = array(
                'all' => array('label'=>app::get('base')->_('订单查看'), 'filter'=>array('status'=>array(0, 3), 'confirm'=>array(1, 3)), 'href'=>$this->delivery_link['order_index']),
                'confirm' => array('label'=>app::get('base')->_('订单确认') ,'filter'=>array('status'=>0, 'confirm'=>3), 'href'=>$this->delivery_link['order_confirm']),
                'consign' => array('label'=>app::get('base')->_('订单发货') ,'filter'=>array('status'=>0, 'confirm'=>1), 'href'=>$this->delivery_link['order_consign']),
                'sign' => array('label'=>app::get('base')->_('签收核销') ,'filter'=>array('status'=>3, 'confirm'=>1, 'process_status'=>7, 'is_received'=>1), 'href'=>$this->delivery_link['order_sign']),
                'overtime' => array('label'=>app::get('base')->_('超时订单') ,'filter'=>array('status'=>0, 'confirm'=>array(1, 3), 'create_time|lthan'=>$dly_overtime), 'href'=>$this->delivery_link['overtimeOrders']),
        );

        foreach($sub_menu as $k=>$v)
        {
            //Ajax加载下一页数据,只处理本页
            if($_POST['flag'] == 'ajax' && $curr_view != $k)
            {
                continue;
            }

            if (!IS_NULL($v['filter']))
            {
                $v['filter']    = array_merge($v['filter'], $base_filter);
            }

            //搜索条件
            if($_POST['sel_type'] && $_POST['sel_keywords'])
            {
                switch ($_POST['sel_type'])
                {
                    case 'delivery_bn':
                        $v['filter']['delivery_bn']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                    case 'order_bn':
                        $v['filter']['order_bn']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                    case 'ship_mobile':
                        $v['filter']['ship_mobile']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                    case 'ship_name':
                        $v['filter']['ship_name']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                }
            }

            $count    = $wapDeliveryObj->count($v['filter']);

            $sub_menu[$k]['filter']    = $v['filter'] ? $v['filter'] : null;
            $sub_menu[$k]['count']     = $count;
            $sub_menu[$k]['pageSize']  = ceil($count / $limit);

            $sub_menu[$k]['offset']    = $offset;
            $sub_menu[$k]['limit']     = $limit;
            $sub_menu[$k]['orderby']   = 'delivery_id desc';#排序

            if($k == $curr_view){
                $sub_menu[$k]['curr_view'] = true;
            }else{
                $sub_menu[$k]['curr_view'] = false;
            }
        }

        return $sub_menu;
    }

    /**
     * 条件
     *
     * status 0:未处理  1:打回  2:暂停 3:已发货
     */
    function _views_tab($curr_view)
    {
        $base_filter = array();
        $wapDeliveryObj    = app::get('wap')->model('delivery');

        $page              = intval($_POST['page']) ? intval($_POST['page'])-1 : 0;
        $limit             = $_POST['pageSize'] ? $_POST['pageSize'] : 10;//每页显示数量
        $offset            = $limit * $page;

        $is_super    = kernel::single('desktop_user')->is_super();
        if(!$is_super)
        {
            $branchObj     = kernel::single('o2o_store_branch');
            $branch_ids    = $branchObj->getO2OBranchByUser(true);
            if(empty($branch_ids))
            {
                $this->pagedata['link_url']     = $this->delivery_link['order_index'];
                $this->pagedata['error_msg']    = '操作员没有管辖的仓库';
                echo $this->fetch('auth_error.html');
                exit;
            }
            $base_filter['branch_id']    = $branch_ids;

            $storeMdl = app::get("o2o")->model("store");
            $storeList = $storeMdl->getList("branch_id,name", array("branch_id" => $branch_ids));
            $store_list = array_column($storeList, "name", "branch_id");
            $this->pagedata['store_list'] = $store_list;
        }

        //menu
        $sub_menu = array(
            'all' => array('label' => app::get('base')->_('全部'), 'filter' => array(), 'href' => $this->delivery_link['order_index']),
            'wait_delivery' => array('label' => app::get('base')->_('待发货'), 'filter' => array('status' => 0, 'logi_status' => '0'), 'href' => $this->delivery_link['orderWaitDelivery']),
            'wait_pickup' => array('label' => app::get('base')->_('待揽件'), 'filter' => array('status' => array('0','3'),'logi_status' => '7'), 'href' => $this->delivery_link['orderWaitPickup']),
            'picked' => array('label' => app::get('base')->_('待签收'), 'filter' => array('status' => 3, 'logi_status' => array('1','2','5','6')), 'href' => $this->delivery_link['orderPicked']),
            'delivered' => array('label' => app::get('base')->_('已签收'), 'filter' => array('status' => 3, 'logi_status' => '3'), 'href' => $this->delivery_link['orderDelivered']),
            'cancel' => array('label' => app::get('base')->_('已取消'), 'filter' => array('filter_sql' => '(status="1" or logi_status in ("4", "8"))'), 'href' => $this->delivery_link['orderCancel']),
        );

        foreach($sub_menu as $k=>$v)
        {
            //Ajax加载下一页数据,只处理本页
            if($_POST['flag'] == 'ajax' && $curr_view != $k)
            {
                continue;
            }

            if (!IS_NULL($v['filter']))
            {
                $v['filter']    = array_merge($v['filter'], $base_filter);
            }

            //搜索条件
            if($_POST['sel_type'] && $_POST['sel_keywords'])
            {
                switch ($_POST['sel_type'])
                {
                    case 'delivery_bn':
                        $v['filter']['delivery_bn']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                    case 'order_bn':
                        $v['filter']['order_bn|foot']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                    case 'ship_mobile':
                        $v['filter']['ship_mobile|has']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                    case 'ship_name':
                        $v['filter']['ship_name']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                    case 'branch_id':
                        $v['filter']['branch_id']    = htmlspecialchars(trim($_POST['sel_keywords']));
                    break;
                    case "express_no" :
                        $info = app::get('wap')->model('delivery_bill')->dump(array('logi_no|foot'=>$_POST['sel_keywords']));
                        if (!empty($info)) {
                            $v['filter']['delivery_id'] = $info['delivery_id'];
                        } else {
                            $v['filter']['delivery_id'] = '-1';
                        }
                    break;
                    case "channel":
                        $shop = app::get('ome')->model('shop')->getList('shop_id',array('shop_type'=>$_POST['sel_keywords']));
                        $shopIds = array_column($shop,'shop_id');
                        $v['filter']['shop_id'] = $shopIds;
                    break;
                }
            }

            $count = 0;
            if ($k == 'wait_delivery' || $k == 'wait_pickup') {
                $count = $wapDeliveryObj->count($v['filter']);
            }

            $sub_menu[$k]['filter']    = $v['filter'] ? $v['filter'] : null;
            $sub_menu[$k]['count']     = $count;
            $sub_menu[$k]['pageSize']  = ceil($count / $limit);

            $sub_menu[$k]['offset']    = $offset;
            $sub_menu[$k]['limit']     = $limit;

            if ($curr_view == 'wait_delivery') {
                $sub_menu[$k]['orderby']   = 'order_createtime asc'; #排序
            } else {
                $sub_menu[$k]['orderby']   = 'order_createtime desc'; #排序
            }

            if($k == $curr_view){
                $sub_menu[$k]['curr_view'] = true;
            }else{
                $sub_menu[$k]['curr_view'] = false;
            }
        }

        return $sub_menu;
    }

    public function getCreateTimeByDateType($filter)
    {
        $dateType = $_POST['dateType'];
        switch ($dateType) {
            case 'today':
                $filter['create_time|than'] = strtotime(date('Y-m-d'));
                $filter['create_time|lthan'] = time();
                break;
            case 'yesterday':
                $filter['create_time|than'] = strtotime(date('Y-m-d', strtotime('-1 day')));
                $filter['create_time|lthan'] = strtotime(date('Y-m-d 23:59:59', strtotime('-1 day')));
                break;
            case 'month':
                $filter['create_time|than'] = strtotime(date('Y-m-01'));
                $filter['create_time|lthan'] = time();
                break;
            case 'custom':
                if ($_POST['start_time']) {
                    $filter['create_time|than'] = strtotime($_POST['start_time']);
                }
                if ($_POST['end_time']) {
                    $filter['create_time|lthan'] = strtotime($_POST['end_time'] . ' 23:59:59');
                }
                break;
        }

        $this->pagedata['dateType'] = $_POST['dateType'] ? $_POST['dateType'] : 'custom';

        return $filter;
    }

    /**
     * 订单查看
     */
    function index()
    {
        if ($_GET['view']) {
            $this->delivery_type = $_GET['view'];
            $sub_menu = $this->_views_tab($this->delivery_type);
        } else {
            $this->delivery_type = 'all';
            $sub_menu = $this->_views_tab($this->delivery_type);
        }

        $this->pagedata['sub_menu'] = $sub_menu;

        $filter      = $sub_menu[$this->delivery_type]['filter'];

        $filter = $this->getCreateTimeByDateType($filter);

        $title       = $sub_menu[$this->delivery_type]['label'];

        $offset            = $sub_menu[$this->delivery_type]['offset'];
        $limit             = $sub_menu[$this->delivery_type]['limit'];

        #仓库对应_发货单列表
        $wapDeliveryLib    = kernel::single('wap_delivery');

        $dataList          = $wapDeliveryLib->getList($filter, $offset, $limit, $sub_menu[$this->delivery_type]['orderby'], $this->delivery_type);

        $this->pagedata['title']       = $title;
        $this->pagedata['dataList']    = $dataList;
        $this->pagedata['pageSize']    = $sub_menu[$this->delivery_type]['pageSize'];

        $this->pagedata['link_url']    = $sub_menu[$this->delivery_type]['href'];

        //baidu map button show or not
        $baidu_map_show = app::get('o2o')->getConf('o2o.baidumap.show');
        if($baidu_map_show=="true"){
            $this->pagedata["baidu_map_show"] = true;
        }

        if(isset($_POST['page']))
        {
            //Ajax加载更多
            $this->display('order/order_list_more.html');
        }
        else
        {
            $corpList = kernel::database()->select("select channel_id,type,name,tmpl_type from sdb_ome_dly_corp where disabled='false' and type not in ('o2o_ship','o2o_pickup')");
            $this->pagedata['corpList'] = json_encode($corpList);

            $shopTypeList = app::get('ome')->model('shop')->getList('shop_type');
            $shopTypeListMap = ome_shop_type::get_shop_type();
            $channelListTmp = array();
            foreach($shopTypeList as $k=>$v){
                if ($v['shop_type'] && $shopTypeListMap[$v['shop_type']]) {
                    $channelListTmp[$v['shop_type']] = $shopTypeListMap[$v['shop_type']];
                }
            }
            $channel = array();
            foreach($channelListTmp as $k=>$v){
                $channel[] = array(
                    'value' => $k,
                    'label' => $v
                );
            }

            $this->pagedata['channelList'] = $channel;

            //门店核单拒绝原因
            $reasonObj    = app::get('o2o')->model('refuse_reason');
            $refuse_reasons  = $reasonObj->getList('*', array('disabled'=>'false'), 0, 100);
            $this->pagedata['refuse_reasons']    = $refuse_reasons;

            $this->display('order/order_list.html');
        }
    }

    function getCorpList(){
        $store_id = $_POST['store_id'];

        $storeObj = app::get('o2o')->model('store');
        $storeInfo = $storeObj->dump(array('store_id'=>$store_id), 'is_default_month_accoun');
        $storeCorpList = $storeObj->db->select("SELECT * FROM sdb_o2o_store_corp WHERE store_id=" . $store_id);
        $corpCodeArr = array();
        $default_corp_code = '';
        if ($storeCorpList) {
            foreach($storeCorpList as $row) {

                $corpCodeArr[] = $row['corp_code'];

                if ($row['is_default'] == '1') {
                    // 默认的物流公司
                    $default_corp_code = $row['corp_code'];
                }

                $defaultCorp[$row['corp_code']]['product_type'] = $row['corp_product'];
                $defaultCorp[$row['corp_code']]['corp_month_account'] = $row['corp_month_account'];
                if ($row['corp_month_account_default'] == '小镇') {
                    $defaultCorp[$row['corp_code']]['default_corp_month_account'] = 'default';
                } else {
                    $defaultCorp[$row['corp_code']]['default_corp_month_account'] = $row['corp_month_account'];
                }
            }
        }

        if ($corpCodeArr) {
            // 获取物流公司
            $corpList = kernel::database()->select("select channel_id,type,name,tmpl_type from sdb_ome_dly_corp where disabled='false' and tmpl_type='electron' and `type` in ('" . implode("','", $corpCodeArr) . "')");
            foreach ($corpList as $k => $row) {
                $product_type_list = kernel::single('logisticsmanager_waybill_func')->corpCode2ChannelService();
                $corpList[$k]['product_type'] = $product_type_list[$row['type']]['product_type'];
            }
        } else {
            $corpList = null;
        }

        echo json_encode(array('rsp' => 'succ', 'storeInfo' => $storeInfo, 'corpList' => $corpList, 'default_corp_code' => $default_corp_code, 'default_corp' => $defaultCorp));exit;
    }

    function decryptAddress()
    {
        $orderId = $_POST['order_id'];
        $type = $_POST['action'];
        $field = 'order_bn,shop_id,shop_type,ship_tel,ship_mobile,ship_addr,ship_name,ship_area';
        $data = app::get('ome')->model('orders')->db_dump(array('order_id' => $orderId), $field);
        if (!$data) {
            echo json_encode(array('rsp' => 'fail', 'msg' => '订单号不存在'));exit;
        }

        if ($data['shop_type'] == 'luban') {
            $jyInfo = kernel::single('ome_bill_label')->getBillLabelInfo($orderId, 'order', 'XJJY');
            if ($jyInfo) {
                echo json_encode(array('rsp' => 'fail', 'msg' => '中转订单请联系客服修改直邮后再进行操作'));exit;
            }
        }

        // mainland:北京/顺义区/后沙峪地区:3268
        $ship_area_str = '';
        if ($data['ship_area']) {
            $ship_area = explode(":", $data['ship_area']);
            $ship_area_str = str_replace("/", "", $ship_area[1]);
        }

        if ($type == 'show') {
            // 解密
            $decrypt_data = kernel::single('ome_security_router', $data['shop_type'])->decrypt(array(
                'ship_tel'    => $data['ship_tel'],
                'ship_mobile' => $data['ship_mobile'],
                'ship_addr'   => $data['ship_addr'],
                'shop_id'     => $data['shop_id'],
                'order_bn'    => $data['order_bn'],
                'ship_name' => $data['ship_name'],
            ), 'order', true);

            if ($decrypt_data['rsp'] && $decrypt_data['rsp'] == 'fail') {
                $errArr = json_decode($decrypt_data['err_msg'], true);
                $msg = $errArr['data']['decrypt_infos'][0]['err_msg'] ? $errArr['data']['decrypt_infos'][0]['err_msg'] : '解密失败,订单已关闭或者解密额度不足';
                $result = [
                    'rsp' => 'fail',
                    'err_data' => $decrypt_data,
                    'msg' => $msg
                ];
                exit(json_encode($result, JSON_UNESCAPED_UNICODE));
            }

            $res = [
                'rsp' => 'succ',
                'data' => [
                    'ship_name' => $decrypt_data['ship_name'],
                    'ship_tel' => $decrypt_data['ship_tel'],
                    'ship_mobile' => $decrypt_data['ship_mobile'],
                    'ship_addr' => $ship_area_str.$decrypt_data['ship_addr']
                ]
            ];
        } else {
            $res = [
                'rsp' => 'succ',
                'data' => [
                    'ship_name' => $data['ship_name'],
                    'ship_tel' => $data['ship_tel'],
                    'ship_mobile' => $data['ship_mobile'],
                    'ship_addr' => $ship_area_str.$data['ship_addr']
                ]
            ];
        }
        echo json_encode($res);exit;
    }

    /**
     * 订单确认
     */
    function confirm()
    {
        $this->delivery_type    = 'confirm';

        $sub_menu    = $this->_views_confirm($this->delivery_type);
        $filter      = $sub_menu[$this->delivery_type]['filter'];
        $title       = $sub_menu[$this->delivery_type]['label'];

        $offset            = $sub_menu[$this->delivery_type]['offset'];
        $limit             = $sub_menu[$this->delivery_type]['limit'];

        #仓库对应_发货单列表
        $wapDeliveryLib    = kernel::single('wap_delivery');

        $dataList          = $wapDeliveryLib->getList($filter, $offset, $limit, $sub_menu[$this->delivery_type]['orderby'], $this->delivery_type);

        $this->pagedata['title']       = $title;
        $this->pagedata['dataList']    = $dataList;
        $this->pagedata['pageSize']    = $sub_menu[$this->delivery_type]['pageSize'];

        $this->pagedata['link_url']    = $sub_menu[$this->delivery_type]['href'];

        //baidu map button show or not
        $baidu_map_show = app::get('o2o')->getConf('o2o.baidumap.show');
        if($baidu_map_show=="true"){
            $this->pagedata["baidu_map_show"] = true;
        }

        if($offset > 0)
        {
            //Ajax加载更多
            $this->display('order/order_list_more.html');
        }
        else
        {
            //门店核单拒绝原因
            $reasonObj    = app::get('o2o')->model('refuse_reason');
            $refuse_reasons  = $reasonObj->getList('*', array('disabled'=>'false'), 0, 100);
            $this->pagedata['refuse_reasons']    = $refuse_reasons;

            $this->display('order/order_list.html');
        }
    }

    /**
     * 订单发货
     */
    function consign()
    {
        $this->delivery_type    = 'consign';

        $sub_menu    = $this->_views_confirm($this->delivery_type);
        $filter      = $sub_menu[$this->delivery_type]['filter'];
        $title       = $sub_menu[$this->delivery_type]['label'];

        $offset            = $sub_menu[$this->delivery_type]['offset'];
        $limit             = $sub_menu[$this->delivery_type]['limit'];

        #仓库对应_发货单列表
        $wapDeliveryLib    = kernel::single('wap_delivery');

        $dataList          = $wapDeliveryLib->getList($filter, $offset, $limit, $sub_menu[$this->delivery_type]['orderby'], $this->delivery_type);

        $this->pagedata['title']       = $title;
        $this->pagedata['dataList']    = $dataList;
        $this->pagedata['pageSize']    = $sub_menu[$this->delivery_type]['pageSize'];

        $this->pagedata['link_url']    = $sub_menu[$this->delivery_type]['href'];

        //baidu map button show or not
        $baidu_map_show = app::get('o2o')->getConf('o2o.baidumap.show');
        if($baidu_map_show=="true"){
            $this->pagedata["baidu_map_show"] = true;
        }

        if($offset > 0)
        {
            //Ajax加载更多
            $this->display('order/order_list_more.html');
        }
        else
        {
            $this->display('order/order_list.html');
        }
    }

    /**
     * 确认拒单
     *
     * @param intval  $delivery_id
     * @return json
     */
    function doRefuse()
    {
        $delivery_id    = intval($_POST['delivery_id']);
        $redirect_url   = ($_POST['backUrl'] ? $_POST['backUrl'] : $this->delivery_link['order_confirm']);
        if(empty($delivery_id))
        {
            echo json_encode(array('res'=>'error', 'msg'=>'无效操作'));
            exit;
        }

        $wapDeliveryObj    = app::get('wap')->model('delivery');
        $deliveryInfo      = $wapDeliveryObj->dump(array('delivery_id'=>$delivery_id), '*');
        if(empty($deliveryInfo))
        {
            echo json_encode(array('res'=>'error', 'msg'=>'没有相关发货单'));
            exit;
        }
        elseif($deliveryInfo['status'] > 0 || $deliveryInfo['confirm'] != 3)
        {
            echo json_encode(array('res'=>'error', 'msg'=>'该发货单无法继续操作'));
            exit;
        }

        $dlyProcessLib = kernel::single('wap_delivery_process');

        //组织参数
        $params = array_merge(array('delivery_id'=>$delivery_id), $deliveryInfo);

        $refuse_reason_id    = intval($_POST['refuse_reason_id']);
        if(empty($refuse_reason_id))
        {
            echo json_encode(array('res'=>'error', 'msg'=>'请选择拒单理由'));
            exit;
        }

        //拒绝原因
        $params['reason_id']   = $refuse_reason_id;

        if($dlyProcessLib->refuse($params)){

            //task任务更新统计数据
            $wapDeliveryLib    = kernel::single('wap_delivery');
            $wapDeliveryLib->taskmgr_statistic('refuse');

            echo json_encode(array('res'=>'succ', 'status'=>'已拒绝', 'msg'=>'已拒绝成功'));
            exit;
        }else{
            echo json_encode(array('res'=>'error', 'msg'=>'门店拒绝失败'));
            exit;
        }
    }

    /**
     * 立即接单
     *
     * @return json
     */
    function doConfirm()
    {
        $delivery_id    = intval($_POST['delivery_id']);
        $redirect_url   = ($_POST['backUrl'] ? $_POST['backUrl'] : $this->delivery_link['order_confirm']);
        if(empty($delivery_id))
        {
            echo json_encode(array('res'=>'error', 'msg'=>'无效操作'));
            exit;
        }

        $wapDeliveryObj    = app::get('wap')->model('delivery');
        $deliveryInfo      = $wapDeliveryObj->dump(array('delivery_id'=>$delivery_id), '*');
        if(empty($deliveryInfo))
        {
            echo json_encode(array('res'=>'error', 'msg'=>'没有相关发货单'));
            exit;
        }
        elseif($deliveryInfo['status'] > 0 || $deliveryInfo['confirm'] != 3)
        {
            echo json_encode(array('res'=>'error', 'msg'=>'该发货单无法继续操作'));
            exit;
        }

        $dlyProcessLib = kernel::single('wap_delivery_process');

        //组织参数
        $params = array_merge(array('delivery_id'=>$delivery_id), $deliveryInfo);

        if($dlyProcessLib->accept($params)){

            //task任务更新统计数据
            $wapDeliveryLib    = kernel::single('wap_delivery');
            $wapDeliveryLib->taskmgr_statistic('confirm');

            echo json_encode(array('res'=>'succ', 'status'=>'已确认', 'msg'=>'订单已接收', 'delivery_bn'=>$deliveryInfo['delivery_bn']));
            exit;
        }else{
            echo json_encode(array('res'=>'error', 'msg'=>'门店确认失败'));
            exit;
        }
    }

    public function getConfirmDeliveryItems(){
        $delivery_id = $_POST['delivery_id'];

        #仓库对应_发货单列表
        $wapDeliveryLib    = kernel::single('wap_delivery');

        $deliveryObj = app::get('wap')->model('delivery');
        $delivery = $deliveryObj->dump(array('delivery_id' => $delivery_id), 'delivery_id,delivery_bn,outer_delivery_bn,status');

        $delivery_items = $wapDeliveryLib->getDeliveryItemList($delivery['outer_delivery_bn']);

        echo json_encode($delivery_items);exit;
    }

    /**
     * 立即发货
     *
     * @return json
     */
    function doConsign()
    {
        $delivery_id    = intval($_POST['delivery_id']);
        $redirect_url   = ($_POST['backUrl'] ? $_POST['backUrl'] : $this->delivery_link['order_consign']);
        if(empty($delivery_id))
        {
            echo json_encode(array('res'=>'error', 'msg'=>'无效操作'));
            exit;
        }
        $filter    = array('delivery_id'=>$delivery_id);

        #管理员对应仓库
        $is_super = kernel::single('desktop_user')->is_super();
        if(!$is_super){
            $branchObj     = kernel::single('o2o_store_branch');
            $branch_ids    = $branchObj->getO2OBranchByUser(true);
            if(empty($branch_ids))
            {
                $error_msg    = '操作员没有管辖的仓库';

                echo json_encode(array('res'=>'error', 'msg'=>$error_msg));
                exit;
            }
            $filter['branch_id'] = $branch_ids;
        }

        $wapDeliveryObj    = app::get('wap')->model('delivery');
        $deliveryInfo      = $wapDeliveryObj->dump($filter, '*');
        $deliveryInfo['status']    = intval($deliveryInfo['status']);

        if(empty($deliveryInfo))
        {
            $error_msg    = '没有此发货单或没仓库权限,请检查';
        }
        elseif($deliveryInfo['confirm'] != 1)
        {
            //$error_msg = "该发货单还未确认,不能进行操作";

            //if($deliveryInfo['confirm'] == 2){
            //    $error_msg = "该发货单已被拒绝,不能进行操作";
            //}
        }
        elseif($deliveryInfo['status'] !== 0)
        {
            $error_msg    = '该发货单状态不正确,不能进行操作';

            if($deliveryInfo['status'] == 3){
                $error_msg    = '该发货单已发货,不能进行操作';
            }
        }

        // logi_no
        $omeDelivery = app::get('ome')->model('delivery')->dump(array('delivery_bn' => $deliveryInfo['outer_delivery_bn']), 'logi_no,status');
        if ($omeDelivery['status'] == 'succ') {
            $error_msg = '该发货单已发货，无需重复发货';
        } elseif (in_array($omeDelivery['status'], array('cancel','back','return_back'))) {
            $error_msg = '发货单:'.$deliveryInfo['delivery_bn'].'已撤销';
        } elseif (!$omeDelivery['logi_no']) {
            $error_msg = '该发货单无快递单号';
        }

        $deliveryInfo['logi_no'] = $omeDelivery['logi_no'];

        //错误提示
        if($error_msg)
        {
            echo json_encode(array('res'=>'error', 'msg'=>$error_msg));
            exit;
        }

        $isCheckStore = kernel::single('wap_delivery_process')->checkDeliveryItemStore($delivery_id);
        if (!$isCheckStore) {
            echo json_encode(array('res' => 'error', 'msg' => '库存不足，请联系库存管理员处理'));
            exit;
        }

        $deliveryInfo['order_number']  = 1;

        //执行发货
        $dlyProcessLib  = kernel::single('wap_delivery_process');
        $res            = $dlyProcessLib->consign($deliveryInfo);
        if($res){

            //task任务更新统计数据
            $wapDeliveryLib    = kernel::single('wap_delivery');
            $wapDeliveryLib->taskmgr_statistic('consign');

            echo json_encode(array('res'=>'succ', 'msg'=>'发货成功'));
            exit;
        }else {
            echo json_encode(array('res'=>'error', 'msg'=>'发货失败'));
            exit;
        }
    }

    /**
     * 重发提货校验码
     *
     * @return json
     */
    function sendMsg()
    {
        //开启销单校验码才能重新生成校验码
        if(app::get('o2o')->getConf('o2o.delivery.confirm.code') != "true"){
            echo json_encode(array('res'=>'error', 'msg'=>'请开启销单校验码'));
            exit;
        }

        $delivery_bn    = $_POST['delivery_bn'];

        if(empty($delivery_bn))
        {
            echo json_encode(array('res'=>'error', 'msg'=>'无效操作'));
            exit;
        }

        $wapDeliveryObj    = app::get('wap')->model('delivery');
        $deliveryInfo      = $wapDeliveryObj->dump(array('delivery_bn'=>$delivery_bn), '*');
        if(empty($deliveryInfo))
        {
            echo json_encode(array('res'=>'error', 'msg'=>'没有相关发货单'));
            exit;
        }
        elseif($deliveryInfo['status'] != 3 || $deliveryInfo['confirm'] != 1 || $deliveryInfo['process_status'] != 7)
        {
            echo json_encode(array('res'=>'error', 'msg'=>'发货单还没有发货'));
            exit;
        }
        elseif($deliveryInfo['is_received'] == 2)
        {
            echo json_encode(array('res'=>'error', 'msg'=>'发货单已签收完成'));
            exit;
        }

        $dlyProcessLib = kernel::single('wap_delivery_process');
        $res    = $dlyProcessLib->reSendMsg($deliveryInfo);
        if($res)
        {
            echo json_encode(array('res'=>'succ', 'msg'=>'提货校验码发送成功'));
            exit;
        }
        else
        {
            echo json_encode(array('res'=>'error', 'msg'=>'提货校验码发送失败'));
            exit;
        }
    }

    /**
     * 显示订单详情
     */
    function showOrderInfo()
    {
        $filehtml    = 'order/sign_order_info.html';

        $delivery_bn    = $_POST['delivery_bn'];
        $flag           = $_POST['flag'];
        $error_msg      = '';

        //先检查发货单号 和 管理员对应仓库
        if(empty($delivery_bn))
        {
            $error_msg    = '请填写发货单号';

            $this->pagedata['error_msg']    = $error_msg;
            $this->display($filehtml);
            exit;
        }

        $filter    = array('delivery_bn'=>$delivery_bn);

        #管理员对应仓库
        $is_super = kernel::single('desktop_user')->is_super();
        if(!$is_super)
        {
            $branchObj     = kernel::single('o2o_store_branch');
            $branch_ids    = $branchObj->getO2OBranchByUser(true);
            if(empty($branch_ids)){
                $error_msg    = '操作员没有管辖的仓库';

                $this->pagedata['error_msg']    = $error_msg;
                $this->display($filehtml);
                exit;
            }

            $filter['branch_id'] = $branch_ids;
        }

        $wapDeliveryObj    = app::get('wap')->model('delivery');
        $deliveryInfo      = $wapDeliveryObj->dump($filter, '*');

        if(empty($deliveryInfo))
        {
            $error_msg    = '没有此发货单,请检查';

            $this->pagedata['error_msg']    = $error_msg;
            $this->display($filehtml);
            exit;
        }

        #获取发货仓库对应的门店店铺信息
        $wapDeliveryLib    = kernel::single('wap_delivery');
        $dlyProcessLib     = kernel::single('wap_delivery_process');
        $branchShopInfo    = $wapDeliveryLib->getBranchShopInfo($deliveryInfo['branch_id']);

        $deliveryInfo['order_number']  = 1;

        #合并数据
        $result    = array_merge($deliveryInfo, $branchShopInfo);
        unset($data['wms_id'], $data['store_id'], $data['area'], $data['confirm'], $data['branch_id']);

        //显示发货单信息
        $result['dly_status']    = $wapDeliveryLib->formatDeliveryStatus('status', $result['status']);
        $result['dly_confirm']   = $wapDeliveryLib->formatDeliveryStatus('confirm', $result['confirm']);

        #获取订单信息
        $result['order_info']    = $wapDeliveryLib->get_order_info($result['order_bn'], $result['is_cod']);

        #获取发货单明细
        $result['delivery_items']    = $wapDeliveryLib->getDeliveryItemList($result['outer_delivery_bn']);

        #履约超时时间
        $result['dly_overtime']    = $wapDeliveryLib->getDeliveryOvertime($result['create_time']);

        //百度地图
        $baidu_map_show = app::get('o2o')->getConf('o2o.baidumap.show');
        if($baidu_map_show == 'true'){
            $result['show_map'] = true;
        }

        $this->pagedata['dlyinfo']    = $result;
        $this->display($filehtml);
        exit;
    }

    /**
     * 签收核销列表
     */
    function sign()
    {
        $this->delivery_type    = 'sign';

        $sub_menu    = $this->_views_confirm($this->delivery_type);
        $filter      = $sub_menu[$this->delivery_type]['filter'];
        $title       = $sub_menu[$this->delivery_type]['label'];

        $offset            = $sub_menu[$this->delivery_type]['offset'];
        $limit             = $sub_menu[$this->delivery_type]['limit'];

        #仓库对应_发货单列表
        $wapDeliveryLib    = kernel::single('wap_delivery');

        $dataList          = $wapDeliveryLib->getList($filter, $offset, $limit, $sub_menu[$this->delivery_type]['orderby'], $this->delivery_type);

        $this->pagedata['title']       = $title;
        $this->pagedata['dataList']    = $dataList;
        $this->pagedata['pageSize']    = $sub_menu[$this->delivery_type]['pageSize'];

        $this->pagedata['link_url']    = $sub_menu[$this->delivery_type]['href'];

        //baidu map button show or not
        $baidu_map_show = app::get('o2o')->getConf('o2o.baidumap.show');
        if($baidu_map_show=="true"){
            $this->pagedata["baidu_map_show"] = true;
        }

        if($offset > 0)
        {
            //Ajax加载更多
            $this->display('order/order_list_more.html');
        }
        else
        {
            $this->display('order/order_list.html');
        }
    }

    /**
     * 核销页面
     */
    function signPage()
    {
        $wapDeliveryObj    = app::get('wap')->model('delivery');

        $delivery_id    = intval($_GET['delivery_id']);

        //发货单
        if($delivery_id)
        {
            $deliveryInfo      = $wapDeliveryObj->dump(array('delivery_id'=>$delivery_id), '*');
        }

        $this->pagedata['deliveryInfo']    = $deliveryInfo;

        //销单校验码开关 关闭状态不显示相关校验码input/button
        if(app::get('o2o')->getConf('o2o.delivery.confirm.code') == "true"){
            $this->pagedata["code_html_show"] = true;
        }

        $this->display('order/sign.html');
    }

    /**
     * 最终签收
     *
     * @return json
     */
    function doSign()
    {
        $delivery_bn    = $_POST['delivery_bn'];
        $flag           = $_POST['flag'];
        $error_msg      = '';

        //先检查发货单号 和 管理员对应仓库
        if(empty($delivery_bn))
        {
            $error_msg    = '请填写发货单号';

            echo json_encode(array('error'=>true, 'message'=>$error_msg, 'redirect'=>null));
            exit;
        }

        $filter    = array('delivery_bn'=>$delivery_bn);

        #管理员对应仓库
        $is_super = kernel::single('desktop_user')->is_super();
        if(!$is_super){
            $branchObj     = kernel::single('o2o_store_branch');
            $branch_ids    = $branchObj->getO2OBranchByUser(true);
            if(empty($branch_ids)){
                $error_msg    = '操作员没有管辖的仓库';

                echo json_encode(array('error'=>true, 'message'=>$error_msg, 'redirect'=>null));
                exit;
            }

            $filter['branch_id'] = $branch_ids;
        }

        $wapDeliveryObj    = app::get('wap')->model('delivery');
        $deliveryInfo      = $wapDeliveryObj->dump($filter, '*');

        if(empty($deliveryInfo))
        {
            $error_msg    = '没有此发货单,请检查';
        }
        elseif($deliveryInfo['status'] != 3 || $deliveryInfo['confirm'] != 1 || $deliveryInfo['process_status'] != 7)
        {
            $error_msg    = '发货单还没有发货';
        }
        elseif($deliveryInfo['is_received'] == 2)
        {
            $error_msg    = '发货单已签收完成';
        }

        //错误提示
        if($error_msg)
        {
            echo json_encode(array('error'=>true, 'message'=>$error_msg, 'redirect'=>null));
            exit;
        }

        $deliveryInfo['order_number']  = 1;

        //开启销单校验码需检查校验码
        if(app::get('o2o')->getConf('o2o.delivery.confirm.code') == "true"){
            $code = $_POST['sms_code'];
            if(empty($code)){
                $error_msg    = '请填写校验码';
            }
            $wapDeliveryCodeObj = app::get('wap')->model('delivery_code');
            $rs_code = $wapDeliveryCodeObj->dump(array('delivery_bn'=>$delivery_bn), '*');
            if($rs_code['code'] != $code){
                $error_msg    = '校验码填写错误';
            }elseif($rs_code['status'] == 1){
                $error_msg    = '校验码已使用';
            }

            //错误提示
            if($error_msg)
            {
                echo json_encode(array('error'=>true, 'message'=>$error_msg, 'flag'=>'sms_code', 'redirect'=>null));
                exit;
            }
        }

        //执行签收
        $dlyProcessLib    = kernel::single('wap_delivery_process');
        $res              = $dlyProcessLib->sign($deliveryInfo);
        if($res){

            //task任务更新统计数据
            $wapDeliveryLib    = kernel::single('wap_delivery');
            $wapDeliveryLib->taskmgr_statistic('sign');

            echo json_encode(array('success'=>true, 'message'=>'收货成功', 'redirect'=>$this->delivery_link['order_sign']));
            exit;
        }else {
            echo json_encode(array('error'=>true, 'message'=>'收货失败', 'flag'=>'sms_code', 'redirect'=>null));
            exit;
        }
    }

    /**
     * 调用百度地图接口 显示当前位置到门店的驾车路线
     */
    function showMapByBaidu(){
        $this->title = '当前位置到门店的路线';
        $this->pagedata['ship_addr'] = $_GET["store_addr"];
        $this->pagedata['baidu_map_key'] = app::get('o2o')->getConf('o2o.baidumap.ak');

        $this->display('order/baidumap_to_store.html');
    }

    /**
     * 超时订单列表
     */
    function overtimeOrders()
    {
        $this->delivery_type    = 'overtime';

        $sub_menu    = $this->_views_confirm($this->delivery_type);
        $filter      = $sub_menu[$this->delivery_type]['filter'];
        $title       = $sub_menu[$this->delivery_type]['label'];

        $offset            = $sub_menu[$this->delivery_type]['offset'];
        $limit             = $sub_menu[$this->delivery_type]['limit'];

        #仓库对应_发货单列表
        $wapDeliveryLib    = kernel::single('wap_delivery');

        $dataList          = $wapDeliveryLib->getList($filter, $offset, $limit, $sub_menu[$this->delivery_type]['orderby']);

        $this->pagedata['title']       = $title;
        $this->pagedata['dataList']    = $dataList;
        $this->pagedata['pageSize']    = $sub_menu[$this->delivery_type]['pageSize'];

        $this->pagedata['link_url']    = $sub_menu[$this->delivery_type]['href'];

        //baidu map button show or not
        $baidu_map_show = app::get('o2o')->getConf('o2o.baidumap.show');
        if($baidu_map_show=="true"){
            $this->pagedata["baidu_map_show"] = true;
        }

        if($offset > 0)
        {
            //Ajax加载更多
            $this->display('order/order_list_more.html');
        }
        else
        {
            //门店核单拒绝原因
            $reasonObj    = app::get('o2o')->model('refuse_reason');
            $refuse_reasons  = $reasonObj->getList('*', array('disabled'=>'false'), 0, 100);
            $this->pagedata['refuse_reasons']    = $refuse_reasons;

            $this->display('order/order_list.html');
        }
    }

    public function onlineDelivery() {
        $delivery_id = $_REQUEST['delivery_id'];
        #仓库对应_发货单列表
        $wapDeliveryLib    = kernel::single('wap_delivery');
        $deliveryObj = app::get('wap')->model('delivery');
        $delivery = $deliveryObj->dump(array('delivery_id' => $delivery_id), 'delivery_id,delivery_bn,outer_delivery_bn,status,branch_id');


        $storeObj       = app::get('o2o')->model('store');
        $storeInfo      = $storeObj->dump(array('branch_id' => $delivery['branch_id']), 'store_id');
        $this->pagedata['store_id'] = $storeInfo['store_id'];

        $delivery_items = $wapDeliveryLib->getDeliveryItemList($delivery['outer_delivery_bn']);

        $this->pagedata['title'] = '呼叫快递';
        $this->pagedata['delivery_items'] = $delivery_items;
        $this->pagedata['delivery_id'] = $delivery_id;
        $this->display('order/order_online_delivery.html');
    }

    public function  outOfStock() {

        $delivery_id = $_REQUEST['delivery_id'];
        #仓库对应_发货单列表
        $wapDeliveryLib    = kernel::single('wap_delivery');
        $deliveryObj = app::get('wap')->model('delivery');
        $delivery = $deliveryObj->dump(array('delivery_id' => $delivery_id), 'delivery_id,delivery_bn,outer_delivery_bn,status');

        $delivery_items = $wapDeliveryLib->getDeliveryItemList($delivery['outer_delivery_bn']);

        $this->pagedata['title'] = '待发货';
        $this->pagedata['delivery_items'] = $delivery_items;
        $this->pagedata['delivery_id'] = $delivery_id;
        $this->display('order/order_outofstock.html');
    }

    public function doOutOfStock() {
        $delivery_id = $_POST['delivery_id'];
        $items = $_POST['items'];

        if (empty($items)) {
            $this->error('请填写缺货数量');
        }

        $omeDeliveryItemObj = app::get('ome')->model('delivery_items_detail');
        $orderItemObj = app::get('ome')->model('order_items');
        $update_items = [];
        $orderId = 0;
        foreach ($items as $item_id => $quantity) {
            if ($quantity <= 0) {
                $this->error('缺货数量不能小于等于0');
            }
            $omeDeliveryItemInfo = $omeDeliveryItemObj->dump(array('delivery_item_id' => $item_id), 'order_item_id');
            $orderItemInfo = $orderItemObj->dump(array('item_id' => $omeDeliveryItemInfo['order_item_id']), 'item_id,order_id,nums,sendnum,bn');
            $orderId = $orderItemInfo['order_id'];
            // 剩余可发货数量
            $sendnum = $orderItemInfo['quantity'] - $orderItemInfo['sendnum'];
            if ($sendnum < $quantity) {
                $this->error($orderItemInfo['bn'] . '缺货数量超过购买数量');
            } else {
                $update_items[] = array(
                    'item_id' => $orderItemInfo['item_id'],
                    'quantity' => $quantity,
                );
            }
        }

        kernel::database()->beginTransaction();
        $error_msg = '';

        $orderInfo = app::get('ome')->model('orders')->dump(array('order_id' => $orderId), 'group_id,op_id');

        $rs = kernel::single('o2o_store_pick')->refuseDelivery([$delivery_id], $error_msg);
        if ($rs) {
            foreach ($update_items as $row) {
                $orderItemObj->update(array('is_lack' => 1, 'lack_num' => $row['quantity']), array('item_id' => $row['item_id']));
            }

            // 缺货取消
            app::get('wap')->model('delivery')->update(array('is_lack_cancel' => 'true'), array('delivery_id' => $delivery_id));

            $orderItemList = $orderItemObj->getList('item_id,order_id,nums,sendnum,bn,lack_num,is_lack', array('order_id' => $orderId));
            $lack_type = '1';
            foreach ($orderItemList as $orderItem) {
                // 可以发货的商品数量
                $sendNum = $orderItem['nums'] - $orderItem['sendnum'] - $orderItem['lack_num'];
                if ($sendNum > 0) {
                    // 部分缺货
                    $lack_type = '2';
                }
            }

            app::get('ome')->model('orders')->update(array('lack_type' => $lack_type, 'group_id'=>$orderInfo['group_id'], 'op_id'=>$orderInfo['op_id']), array('order_id' => $orderId));

            kernel::database()->commit();

            app::get('ome')->model('orders')->renewOrder($orderId);

            $this->success('缺货处理成功');
        } else {
            kernel::database()->rollback();
            $this->error($error_msg);
        }
    }

    public function doCancelDelivery() {
        $delivery_id = $_POST['delivery_id'];
        $error_msg = '';
        $rs = kernel::single('wap_delivery_process')->doCancelDelivery($delivery_id, $error_msg);
        if ($rs) {
            $this->success('取消快递提交成功');
        } else {
            $this->error($error_msg);
        }
    }

    /**
     * 获取微信签名
     */
    public function getWxSign() {
        $url = $_POST['url'];

        $msg = '';
        $wxSign = kernel::single('monitor_wechat_token')->getWxSign($url, $msg);
        if (!$wxSign) {
            $msg = $msg ? $msg : '网络异常，请重试';
            $this->error($msg);
        }

        $this->success('签名成功', $wxSign);
    }

    public function getPendingDeliveryOrders() {
        $page = intval($_POST['page']) ? intval($_POST['page']) : 1;
        $pageSize = $_POST['pageSize'] ? $_POST['pageSize'] : 10;
        
        $filter = array(
            'status' => 0,
            'logi_status' => '0'
        );
        
        // 非超级管理员只能查看有权限的仓库
        $is_super = kernel::single('desktop_user')->is_super();
        if (!$is_super) {
            $branchObj = kernel::single('o2o_store_branch');
            $branch_ids = $branchObj->getO2OBranchByUser(true);
            if (empty($branch_ids)) {
                $this->error('操作员没有管辖的仓库');
                return;
            }
            $filter['branch_id'] = $branch_ids;
        }
        
        $wapDeliveryObj = app::get('wap')->model('delivery');
        $delivery_billObj = app::get('wap')->model('delivery_bill');
        
        // 先获取所有待发货订单
        $deliveries = $wapDeliveryObj->getList('*', $filter, ($page-1)*$pageSize, $pageSize, 'delivery_id desc');
        
        // 获取这些订单的物流单号信息
        $delivery_ids = array_column($deliveries, 'delivery_id');
        $delivery_bills = $delivery_billObj->getList('delivery_id,logi_no', array('delivery_id' => $delivery_ids));
        $delivery_bills_map = array_column($delivery_bills, 'logi_no', 'delivery_id');
        
        // 过滤掉已有物流单号的订单
        $deliveries = array_filter($deliveries, function($delivery) use ($delivery_bills_map) {
            return !empty($delivery_bills_map[$delivery['delivery_id']]);
        });
        
        // 重新计算总数
        $count = count($deliveries);
        
        $this->success('获取成功', array(
            'total' => $count,
            'list' => $deliveries
        ));
    }

    /**
     * 批量呼叫快递页面
     */
    public function batchOnlineDelivery() {
        // 获取选中的订单ID
        $delivery_ids = $_REQUEST['delivery_ids'];
        if (empty($delivery_ids)) {
            $this->pagedata['error_msg'] = '请选择需要呼叫快递的订单';
            $this->pagedata['back_url'] = $this->delivery_link['order_index'];
            echo $this->display('order/order_batch_delivery_error.html');
            exit;
        }

        $delivery_ids = explode(',', $delivery_ids);
        $delivery_ids = array_filter($delivery_ids); // 过滤空值

        if (empty($delivery_ids)) {
            $this->pagedata['error_msg'] = '请选择需要呼叫快递的订单';
            $this->pagedata['back_url'] = $this->delivery_link['order_index'];
            echo $this->display('order/order_batch_delivery_error.html');
            exit;
        }

        // 获取待发货订单数量统计
        $base_filter = array();
        $is_super = kernel::single('desktop_user')->is_super();
        if (!$is_super) {
            $branchObj = kernel::single('o2o_store_branch');
            $branch_ids = $branchObj->getO2OBranchByUser(true);
            if (empty($branch_ids)) {
                $this->pagedata['error_msg'] = '操作员没有管辖的仓库';
                $this->pagedata['back_url'] = $this->delivery_link['order_index'];
                echo $this->display('order/order_batch_delivery_error.html');
                exit;
            }
            $base_filter['branch_id'] = $branch_ids;
        }

        $base_filter['delivery_id'] = $delivery_ids;

        // 待发货订单过滤条件（待发货状态且无获取过物流单号的订单）
        $filter = array_merge($base_filter, array('status' => 0, 'logi_status' => '0'));

        // 按门店统计待发货订单数量
        $store_stats = $this->getWaitDeliveryStatsByStore($filter);

        if (empty($store_stats)) {
            $this->pagedata['error_msg'] = '没有找到符合条件的待呼叫快递订单,选中的单据可能已呼叫过快递';
            $this->pagedata['back_url'] = $this->delivery_link['order_index'];
            echo $this->display('order/order_batch_delivery_error.html');
            exit;
        }

        $this->pagedata['title'] = '批量呼叫快递';
        $this->pagedata['store_stats'] = $store_stats;
        $this->pagedata['store_id'] = $store_stats[0]['store_id'];
        $this->pagedata['store_stats_json'] = json_encode($store_stats[0]);
        $this->pagedata['total_count'] = $store_stats[0]['count'];
        $this->display('order/order_batch_online_delivery.html');
    }

    /**
     * 获取待发货订单按门店统计
     */
    private function getWaitDeliveryStatsByStore($filter) {
        $wapDeliveryObj = app::get('wap')->model('delivery');

        // 获取所有待发货订单
        $deliveries = $wapDeliveryObj->getList('delivery_id,branch_id,order_bn', $filter, 0, 1000);

        // 过滤掉已有物流单号的订单（待呼叫快递单量=待发货状态且无获取过物流单号的订单）
        $has_logi_no = array();
        $delivery_ids = array_column($deliveries, 'delivery_id');
        if (!empty($delivery_ids)) {
            $delivery_bills = app::get('wap')->model('delivery_bill')->getList('delivery_id,logi_no', array('delivery_id' => $delivery_ids));
            foreach($delivery_bills as $item) {
                if (!empty($item['logi_no'])) {
                    $has_logi_no[] = $item['delivery_id'];
                }
            }
        }

        $store_stats = array();
        $storeObj = app::get('o2o')->model('store');

        foreach ($deliveries as $delivery) {
            // 跳过已有物流单号的订单
            if (in_array($delivery['delivery_id'], $has_logi_no)) {
                continue;
            }

            $branch_id = $delivery['branch_id'];
            if (!isset($store_stats[$branch_id])) {
                $storeInfo = $storeObj->dump(array('branch_id' => $branch_id), 'store_id,name');
                if (empty($storeInfo)) {
                    continue; // 如果找不到门店信息，跳过
                }
                $store_stats[$branch_id] = array(
                    'branch_id' => $branch_id,
                    'store_id' => $storeInfo['store_id'],
                    'store_name' => $storeInfo['name'],
                    'count' => 0,
                    'delivery_ids' => array()
                );
            }
            $store_stats[$branch_id]['count']++;
            $store_stats[$branch_id]['delivery_ids'][] = $delivery['delivery_id'];
            $store_stats[$branch_id]['order_bns'][$delivery['delivery_id']] = $delivery['order_bn'];
        }

        return array_values($store_stats);
    }
}
