<?php

class logisticsmanager_waybill_xhs
{

    public function service_code($param)
    {
        $cpCode  = $param['logistics'];
        $service = array(
            'zto'            => array(
                'branchCode'          => array( // 加盟型快递公司一般要求必填
                    'text'       => '网点编码',
                    'code'       => 'branchCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'SVC-INSURE'          => array(
                    'text'       => '保价',
                    'code'       => 'SVC-INSURE',
                    'input_type' => 'checkbox',
                ),
                'SVC-SIGN-CONFIRM'    => array(
                    'text'       => '秘钥签收',
                    'code'       => 'SVC-SIGN-CONFIRM',
                    'input_type' => 'checkbox',
                ),
                'SVC-VIP'             => array(
                    'text'       => 'VIP尊享',
                    'code'       => 'SVC-VIP',
                    'input_type' => 'checkbox',
                ),
                'SVC-TIMING-HIGH'     => array(
                    'text'       => '特快',
                    'code'       => 'SVC-TIMING-HIGH',
                    'input_type' => 'checkbox',
                ),
                'SVC-TIMING-STANDARD' => array(
                    'text'       => '中通标快',
                    'code'       => 'SVC-TIMING-STANDARD',
                    'input_type' => 'checkbox',
                ),
                'STAR-CITY'           => array(
                    'text'       => '同城',
                    'code'       => 'STAR-CITY',
                    'input_type' => 'checkbox',
                ),
                'STAR-COLD'           => array(
                    'text'       => '冷链',
                    'code'       => 'STAR-COLD',
                    'input_type' => 'checkbox',
                ),
                'SVC-STAR'            => array(
                    'text'       => '星联',
                    'code'       => 'SVC-STAR',
                    'input_type' => 'checkbox',
                ),
            ),
            'yuantong'       => array(
                'branchCode' => array( // 加盟型快递公司一般要求必填
                    'text'       => '网点编码',
                    'code'       => 'branchCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
            ),
            'yunda'          => array(
                'branchCode' => array( // 加盟型快递公司一般要求必填
                    'text'       => '网点编码',
                    'code'       => 'branchCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'SVC-INSURE' => array(
                    'text'       => '保价',
                    'code'       => 'SVC-INSURE',
                    'input_type' => 'checkbox',
                ),
            ),
            'shentong'       => array(
                'branchCode' => array( // 加盟型快递公司一般要求必填
                    'text'       => '网点编码',
                    'code'       => 'branchCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'SVC-INSURE' => array(
                    'text'       => '保价',
                    'code'       => 'SVC-INSURE',
                    'input_type' => 'checkbox',
                ),
                'SVC-FRESH'  => array(
                    'text'       => '生鲜件',
                    'code'       => 'SVC-FRESH',
                    'input_type' => 'checkbox',
                ),
            ),
            'ems'            => array(
                'customerCode' => array(
                    'text'       => '月结卡号', // 直营快递公司一般要求必填
                    'code'       => 'customerCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'SVC-INSURE'   => array(
                    'text'       => '保价',
                    'code'       => 'SVC-INSURE',
                    'input_type' => 'checkbox',
                ),
            ),
            'youzhengguonei' => array(
                'customerCode' => array(
                    'text'       => '月结卡号', // 直营快递公司一般要求必填
                    'code'       => 'customerCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'SVC-INSURE'   => array(
                    'text'       => '保价',
                    'code'       => 'SVC-INSURE',
                    'input_type' => 'checkbox',
                ),
            ),
            'jtexpress'      => array(
                'branchCode' => array( // 加盟型快递公司一般要求必填
                    'text'       => '网点编码',
                    'code'       => 'branchCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'SVC-INSURE' => array(
                    'text'       => '保价',
                    'code'       => 'SVC-INSURE',
                    'input_type' => 'checkbox',
                ),
                'SVC-TYD'    => array(
                    'text'       => '兔优达',
                    'code'       => 'SVC-TYD',
                    'input_type' => 'checkbox',
                ),
            ),
            'debangwuliu'    => array(
                'customerCode' => array(
                    'text'       => '月结卡号', // 直营快递公司一般要求必填
                    'code'       => 'customerCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'insurance'    => array(
                    'text'       => '保价',
                    'code'       => 'insurance',
                    'input_type' => 'checkbox',
                ),
            ),
            'SF'             => array(
                'INSURE'       => array(
                    'text'       => '保价',
                    'code'       => 'INSURE',
                    'input_type' => 'checkbox',
                ),
                'PRODUCT-TYPE' => array(
                    'text'       => '产品类型',
                    'code'       => 'PRODUCT-TYPE',
                    'input_type' => 'select',
                    'options'    => array(
                        ''    => '',
                        '1'   => '顺丰特快',
                        '2'   => '顺丰标快',
                        '6'   => '顺丰即日',
                        '31'  => '便利封/袋（特快）',
                        '111' => '顺丰干配',
                        '112' => '顺丰空配',
                        '154' => '重货包裹',
                        '155' => '标准零担',
                        '199' => '特快包裹',
                        '201' => '冷运标快',
                        '202' => '顺丰微小件',
                        '229' => '精温专递',
                        '230' => '标准零担D类',
                        '231' => '陆运包裹',
                        '238' => '纯重特配',
                        '247' => '电商标快',
                        '266' => '顺丰空配（新）',
                        '293' => '特快包裹（新）',
                        '283' => '填舱标快',
                    ),
                ),
            ),
            'FOP'            => array(
                'customerCode' => array(
                    'text'       => '月结卡号', // 直营快递公司一般要求必填
                    'code'       => 'customerCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'insurance'    => array(
                    'text'       => '保价',
                    'code'       => 'INSURE',
                    'input_type' => 'checkbox',
                ),
                'PRODUCT-TYPE' => array(
                    'text'       => '产品类型',
                    'code'       => 'PRODUCT-TYPE',
                    'input_type' => 'select',
                    'options'    => array(
                        ''    => '',
                        '1'   => '顺丰特快',
                        '2'   => '顺丰标快',
                        '111' => '顺丰干配',
                        '153' => '整车直达',
                        '154' => '重货包裹',
                        '155' => '标准零担',
                        '215' => '大票直送',
                        '230' => '标准零担D类',
                        '238' => '纯重特配',
                        '255' => '顺丰卡航',
                        '256' => '顺丰卡航（D类）',
                    ),
                ),
            ),
            'jd'             => array(
                'customerCode' => array(
                    'text'       => '月结卡号', // 直营快递公司一般要求必填
                    'code'       => 'customerCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'SVC-INSURE'   => array(
                    'text'       => '保价',
                    'code'       => 'SVC-INSURE',
                    'input_type' => 'checkbox',
                ),
                'SVC-FRESH'    => array(
                    'text'       => '生鲜温层',
                    'code'       => 'SVC-FRESH',
                    'input_type' => 'select',
                    'options'    => array(
                        'usual'    => '生鲜常温(默认)',
                        'common'   => '生鲜普通',
                        'alive'    => '生鲜鲜活',
                        'control'  => '生鲜控温',
                        'cold'     => '生鲜冷藏',
                        'freezing' => '生鲜冷冻',
                        'deepCool' => '生鲜深冷',
                    ),
                ),
                'PRODUCT-TYPE' => array(
                    'text'       => '产品类型',
                    'code'       => 'PRODUCT-TYPE',
                    'input_type' => 'select',
                    'options'    => array(
                        ''          => '',
                        'ed-m-0001' => '特惠送',
                        'ed-m-0002' => '特快送',
                        'LL-HD-M'   => '生鲜特惠',
                        'LL-SD-M'   => '生鲜特快',
                        'ed-m-0019' => '特惠小件',
                        'fr-m-0004' => '特快重货',
                        'fr-m-0001' => '特快零担',
                        'fr-m-0002' => '特惠重货',
                    ),
                ),
            ),
            'youzhengbiaokuai'=>array(
                'customerCode' => array(
                    'text'       => '月结卡号', // 直营快递公司一般要求必填
                    'code'       => 'customerCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'SVC-COD'   => array(
                    'text'       => '代收货款',
                    'code'       => 'SVC-COD',
                    'input_type' => 'checkbox',
                ),
            ),
            'danniao'=>array(
                'customerCode' => array(
                    'text'       => '月结卡号', // 直营快递公司一般要求必填
                    'code'       => 'customerCode',
                    'input_type' => 'input',
                    'require'    => 'true',
                ),
                'VALUE_INSURED'   => array(
                    'text'       => '保价',
                    'code'       => 'VALUE_INSURED',
                    'input_type' => 'checkbox',
                ),
            ),

        );
        return isset($service[$cpCode]) ? $service[$cpCode] : [];

    }

    /**
     * 打印组件三个接口
     * 获取打印数据 /logistics/waybillApply
     * 获取标准模板 /logistics/templateList
     * 获取自定义模板 /logistics/customTemplateList
     * @return array [description]
     */
    public function template_cfg()
    {
        $arr = array(
            'template_name' => '小红书',
            'shop_name'     => '小红书',
            'print_url'     => 'https://xhswaybill-printer-1251524319.cos.ap-shanghai.myqcloud.com/XHPrintTool/prod/win/xiaohongshu-win.exe',
            // 'template_url'  => '',
            'shop_type'     => 'xhs',
            'control_type'  => 'xhs',
            'request_again' => false,
        );
        return $arr;
    }

    /**
     * 获取物流公司编码
     * @param Sring $logistics_code 物流代码
     * @return array
     */
    public function logistics($logistics_code = '')
    {
        $logistics = [
            'zto'               => ['code' => 'zto', 'name' => '中通速递'],
            'yuantong'          => ['code' => 'yuantong', 'name' => '圆通快递'],
            'yunda'             => ['code' => 'yunda', 'name' => '韵达速递'],
            'jtexpress'         => ['code' => 'jtexpress', 'name' => '极兔速递'],
            'shentong'          => ['code' => 'shentong', 'name' => '申通速递'],
            'youzhengguonei'    => ['code' => 'youzhengguonei', 'name' => '邮政快递'],
            'ems'               => ['code' => 'ems', 'name' => 'EMS'],
            'debangwuliu'       => ['code' => 'debangwuliu', 'name' => '德邦快递'],
            'SF'                => ['code' => 'SF', 'name' => '顺丰速运'],
            'FOP'               => ['code' => 'FOP', 'name' => '顺丰快运'],
            'jd'                => ['code' => 'jd', 'name' => '京东物流'],
            'youzhengbiaokuai'  => ['code' => 'youzhengbiaokuai', 'name' => '邮政电商标快'],
            'danniao'           => ['code' => 'danniao', 'name' => '丹鸟快递'],
        ];

        if (!empty($logistics_code)) {
            return $logistics[$logistics_code];
        }
        return $logistics;
    }
}
