<?php

class logisticsmanager_waybill_func
{
    //获取面单来源渠道
    public function channels($channel_type = null)
    {
        $channels = array(
            'taobao' => array('code' => 'taobao', 'name' => '淘宝'),
            'ems' => array('code' => 'ems', 'name' => 'EMS官方'),
            '360buy' => array('code' => '360buy', 'name' => '京东快递'),
            'sf' => array('code' => 'sf', 'name' => '顺丰'),
            'yunda' => array('code' => 'yunda', 'name' => '韵达'),
            'sto' => array('code' => 'sto', 'name' => '申通'),
            'hqepay' => array('code' => 'hqepay', 'name' => '快递鸟'),
            'unionpay' => array('code' => 'unionpay', 'name' => '银联'),
            'bbd' => array('code' => 'bbd', 'name' => '棒棒达'),
            'customs' => array('code' => 'customs', 'name' => '跨境电商'),
            'jdalpha' => array('code' => 'jdalpha', 'name' => '京东电子面单'),
            'aikucun' => array('code' => 'aikucun', 'name' => '爱库存'),
            'pinjun' => array('code' => 'pinjun', 'name' => '品骏'),
            'pdd' => array('code' => 'pdd', 'name' => '拼多多'),
            'vopjitx' => array('code' => 'vopjitx', 'name' => '唯品会JITX'),
            'yto4gj' => array('code' => 'yto4gj', 'name' => '圆通国际'),
            'douyin' => array('code' => 'douyin', 'name' => '抖音'),
            'wphvip' => array('code' => 'wphvip', 'name' => '唯品会vip'),
            'kuaishou' => array('code' => 'kuaishou', 'name' => '快手'),
            'xhs' => array('code' => 'xhs', 'name' => '小红书'),
            'wxshipin' => array('code' => 'wxshipin', 'name' => '微信视频号'),
            'dewu' => array('code' => 'dewu', 'name' => '得物'),
            'meituan4bulkpurchasing' => array('code' => 'meituan4bulkpurchasing', 'name' => '美团电商'),
        );
        if (!empty($channel_type)) {
            return $channels[$channel_type];
        }
        return $channels;
    }

    // 快递公司映射渠道服务参数
    public function corpCode2ChannelService()
    {
        $data = array(
            'SF' => array(
                'product_type' => array(
                    array('value' => '1', 'label' => '顺丰特快'),
                    array('value' => '2', 'label' => '顺丰标快'),
                    array('value' => '283', 'label' => '填舱标快'),
                ),
                'logistics_code' => array(
                    'luban' => 'shunfeng',
                    'xhs' => 'SF',
                    'wxshipin' => 'SF',
                    'ecos.ecshopx' => 'SF',
                ),
                // 快递鸟 - 通知快递员上门取件映射，不配置映射关系，则默认为：0
                'hqepay_isnotice' => array(
                    '283' => 1,
                ),
            ),
            'JD' => array(
                'product_type' => array(
                    array('value' => 'ed-m-0001', 'label' => '特惠送'),
                    array('value' => 'ed-m-0002', 'label' => '特快送'),
                    array('value' => 'ed-m-0019', 'label' => '特惠小件'),
                    array('value' => 'ed-m-0012', 'label' => '特惠包裹'),
                ),
                'logistics_code' => array(
                    'luban' => 'jd',
                    'xhs' => 'jd',
                    'wxshipin' => 'JD',
                    'ecos.ecshopx' => 'JD',
                ),
                // 快递鸟 - 通知快递员上门取件映射，不配置映射关系，则默认为：1
                'hqepay_isnotice' => array(
                    'ed-m-0001' => 0,
                    'ed-m-0002' => 0,
                    'ed-m-0019' => 0,
                    'ed-m-0012' => 0,
                ),
            ),
        );
        return $data;
    }

    /**
     * 获取平台对应的物流公司产品类型
     * @param string $shopType 平台类型
     * @param string $logistics_code 物流公司
     * @param string $product_type 产品类型
     * @return string
     */
    public function getCorpProductType($shopType, $logistics_code, $product_type, $get_all = false)
    {

        $array = array(
            'wxshipin' => array(
                'JD' => array(
                    'ed-m-0001' => '1',
                    'ed-m-0002' => '2',
                    'ed-m-0019' => '7',
                    'ed-m-0012' => '6',
                ),
            ),
            'ecos.ecshopx' => array(
                'JD' => array(
                    'ed-m-0001' => '1', // 京东标快
                    'ed-m-0002' => '2', // 京东特快
                    'ed-m-0012' => '7', // 特惠包裹
                    'ed-m-0019' => '8', // 特惠小件
                )
            ),
        );
        if ($get_all) {
            $result = $array[$shopType][$logistics_code] ?? [];
        } else {
            $result = empty($array[$shopType][$logistics_code][$product_type]) ? $product_type : $array[$shopType][$logistics_code][$product_type];;
        }
        return $result;
    }

    /**
     * 获取平台对应的物流公司编码
     * @param $shop_type
     * @param $logistics_code
     * @return void
     */
    public function getLogisticsCode($shop_type, $logistics_code)
    {
        if (empty($shop_type)) {
            return $logistics_code;
        }

        # 读取配置数据
        $config = $this->corpCode2ChannelService();
        if (!isset($config[$logistics_code])) {
            return $logistics_code;
        }

        # 获取物流公司编码
        $result = $config[$logistics_code]['logistics_code'][$shop_type];
        return empty($result) ? $logistics_code : $result;
    }

    /**
     * 店铺类型转换
     * @param $shop_type
     * @return string
     */
    public function getShopType($shop_type)
    {
        $result = array(
            'luban' => 'douyin',
            'ecos.ecshopx' => 'hqepay', // 快递鸟
        );
        return $result[$shop_type] ?? $shop_type;
    }

    /**
     * 根据物流公司编码，获得快递类型
     * @param $corp_code
     * @return array
     */
    public function code2channeltype()
    {
        $map = [
            //如果有特殊快递公司编码，则到这里录入下,示例如下：
            //'STO'=>'sto',
        ];
        return $map;
    }

    /**
     * 获取指定服务类型信息
     * @param $logi_type
     * @param $product_type
     * @return mixed
     */
    public function getProductTypeByLogi($shop_type, $logi_type, $product_type)
    {
        if (empty($logi_type) || empty($product_type)) {
            return [];
        }

        if (in_array($shop_type, array('wxshipin', 'ecos.ecshopx'))) {
            $productList = $this->getCorpProductType($shop_type, $logi_type, $product_type, true);
            if (!empty($productList)) {
                $new_product = [];
                foreach ($productList as $k => $v) {
                    $new_product[$v] = $k;
                }
                # 重新设定产品编码
                if (!empty($new_product[$product_type])) {
                    $product_type = $new_product[$product_type];
                }
            }
        }

        # 所有的服务类型配置
        $all_product_list = $this->corpCode2ChannelService();
        if (empty($all_product_list[$logi_type])) {
            return [];
        }

        # 服务类型列表
        $productList = $all_product_list[$logi_type]['product_type'];
        if (empty($productList)) {
            return [];
        }

        # 匹配服务类型
        $result = array_column($productList, null, 'value');
        return $result[$product_type] ?? $product_type;
    }

    /**
     * 获取渠道名称
     * @param $shop_type
     * @return string
     */
    public function getChannelName($shop_type, $name)
    {
        if (empty($name) || ome_func::contains($name, ['抖音', '微信小店', '微信商城', '微信视频号', '商城', '视频号'])) {
            return '';
        }

        $channel = [
            'luban' => '抖音',
            'wxshipin' => '视频号',
            'ecos.ecshopx' => 'AIO商城',
        ];
        $result = $channel[$shop_type] ?? null;
        return empty($result) ? $name : ($name . '-' . $result);
    }
}
