<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$obn = "3729734566904472320,3729734552883962368,3729734584450030848,3729734552475022336,3729734575476323840,3729734560233436928,3729734616673821184,3729734552581709568,3729734551495644160,3729734585821306368,3729734587230335744,3729734581180834048,3729734629259621888,3729734571354626560,3729734587634299648,3729734600846356480,3729734632049875968,3729734634663716864,3729734463025191424,3729734549232821504,3729734693918225664,3729734691537951744,3729734613528613888,3729734694449068032,3729734693360645120,3729734692312327424,3729734692249932800,3729734693008848128,3729734693077006336,3729734692999144960,3729734692070104320,3729734694173815808,3729734695838946304,3729734700529496064,3729734696925541376,3729734695381242880,3729734701606642432,3729734706770876928,3729734694749221888,3729734695610099712,3729734695695292928,3729734697823905024,3729734698485035264,3729734709916868352,3729734727941105664,3729734697795853824,3729734712522317312,3729734729400988416,3729734775489574401,3729734743422023424,3729734783766247168,3729734750934017536,3729734693483067392,3729734821962200064,3729734789074396416,3729734811865194240,3729734802635110400,3729734830033346816,3729734809921659136,3729734804643912960,3729734805316312064,3729734825516341760,3729734851535451648,3729734849104852224,3729734840956367616,3729734920663086336,3729734922074201600,3729734920728619008,3729734933834248192,3729735003523127808,3729734995811906560,3729734872547857408,3729735071397455360,3729735085072718848,3729735116485698816,3729735142600225792,3729735130631512320,3729735158284035584,3729735124109632256,3729735209704895744,3729735186293335296,3729735199335260672,3729735206378288896,3729735242047168768,3729735243885845760,3729735234929963264,3729735282252197888,3729735284661298432,3729735269336620800,3729735306504707840,3729735279268471040,3729735322259037952,3729735316271931648,3729735351613399552,3729735353107619072,3729735381094636800,3729735448540624384,3729735427651941376,3729735427651941376,3729735414573053440,3729735427865059072,3729733825195030272,3729733754680911872,3729733738762477312,3729733825387444224,3729733824875467776,3729733826549003008,3729733828074680320,3729734008629501184,3729733857268082176,3729733827464933632,3729733734070892288,3729733826349768960,3729733829645972480,3729733827117331200,3729733826089982976,3729733829814532096,3729733828069697024,3729733827451040512,3729733825826272768,3729733828185826816,3729733832734028800,3729733832998527232,3729733834935511040,3729733835300413952,3729733994122978816,3729733847871796992,3729733867396544256,3729733966685674752,3729733856026828032,3729733838490449152,3729733963489622016,3729733859588060928,3729733860085606400,3729733835644352512,3729733873740166912,3729733866766343680,3729733845803743232,3729733964815544320,3729733980083330304,3729733970731607552,3729733974385107457,3729733979517368064,3729733964148125696,3729733984279475712,3729734018488219136,3729733987472389376,3729734033763610880,3729734216385705984,3729734231329741312,3729734257535758336,3729734272479534080,3729734280244241152,3729734334072627456,3729734315492124160,3729734356279633920,3729734339930760448,3729734326906920192,3729734340809996288,3729734373171667456,3729734381204282624,3729734406355424256,3729734372689850368,3729734425628516096,3729734442078843904,3729734374807709696,3729734449188712704,3729734441255707904,3729734444083715584,3729734452410982912,3729734444868574976,3729734440657238016,3729734448445797121,3729734442412286720,3729734441047038976,3729734487412978944,3729734445290364160,3729734442832763904,3729734448501634304,3729734441226089984,3729734442462354945,3729734474806211328,3729734463887908096,3729734457677456128,3729734467870928896,3729734483440180224,3729734459392924160,3729734442403634944,3729734529619728896,3729734548055269888,3729734548193687040,3729734525201298176,3729734548216750337,3729734506617385984,3729734506617385984,3729734547713700608,3729734562406865920,3729734549294688256,3729734549485788928,3729734549488415488,3729734547977154560,3729734550741463296,3729733701724937984,3729733705402294528,3729733719298551808,3729733827731529472,3729733838684436480,3729733702106096128,3729733704215573760,3729733573102416896,3729733722023272704,3729733703932717056,3729733701547471104,3729733861784294400,3729733724495296512,3729733738560108032,3729733703305929984,3729733702111335424,3729734009125746176,3729733709298545664,3729733717868034048,3729733726920390656,3729733742206792704,3729733742233266176,3729729779040923904,3729731374055306752,3729729824596303104,3729731937114140928,3729730196798317824,3729730612586749184,3729730892051658752";
$obn = '3729734566904472320';
$bns = explode(',', $obn);
$delMdl = app::get('ome')->model('delivery');
$ordMdl = app::get('ome')->model('orders');
$odMdl = app::get('ome')->model('delivery_order');
$paymentMdl = app::get("ome")->model("payments");
$basicMStockFreezeLib = kernel::single('material_basic_material_stock_freeze');
$oOperation_log = app::get('ome')->model('operation_log');
$count = count($bns);
$notE = 0;
$notEA = 0;
$del = 0;
$memo = '';
$i = 0;
while(true){
    $bnSlice = array_slice($bns, $i*500, 500);
    if(empty($bnSlice)){
        error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n ====订单总计：".$count."，不存在订单：".$notE."，订单状态非active：".$notEA."，存在有效发货单：".$del, 3, DATA_DIR.'/delOrder.log');
        exit;
    }
    foreach ($bnSlice as $order_bn){
        error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n 订单号：".var_export($order_bn, 1)."\n", 3, DATA_DIR.'/delOrder.log');
        $ordInfo = $ordMdl->dump(array("order_bn" => $order_bn), "order_id,logi_no,status,process_status,ship_status");
        error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n 订单信息：".var_export($ordInfo, 1)."\n", 3, DATA_DIR.'/delOrder.log');
        if(empty($ordInfo)){
            error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n 订单号：".var_export($order_bn, 1)."不存在\n", 3, DATA_DIR.'/delOrder.log');
            $notE++;
            continue;
        }
        if($ordInfo['status'] != 'active' || $ordInfo['ship_status'] != '0'){
            error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n 订单号：".$order_bn."=====".var_export($ordInfo, 1)."状态不对\n", 3, DATA_DIR.'/delOrder.log');
            $notEA++;
            continue;
        }
        $doList = $odMdl->getList("*", array("order_id" => $ordInfo['order_id']));
        $dIds = array_column($doList, "delivery_id");
        #有有效发货单取消发货单
        if(!empty($dIds)){
            $delList = $delMdl->getList("delivery_id,delivery_bn", array("delivery_id" => $dIds, "disabled" => "false"));
            if(!empty($delList)){
                $rebackIds = array_column($delList, "delivery_id");
                $rebackBns = array_column($delList, "delivery_bn");
                $rbns = implode(",", $rebackBns);
                $result = $delMdl->rebackDelivery($rebackIds, '');
                if($result){
                    $memo .= '撤销发货单成功！';
                }else{
                    $memo .= '撤销发货单失败！';
                }

                error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n 订单号：".var_export($order_bn, 1)." 发货单号：".var_export($rbns, 1)." 撤销发货单结果：".var_export($result, 1)."\n", 3, DATA_DIR.'/delOrder.log');
                $del++;
            }
        }
        #释放订单冻结
        $freeResult = $basicMStockFreezeLib->delOrderFreeze($ordInfo['order_id']);
        if($freeResult){
            $memo .= ' 释放订单冻结成功！';
        }else{
            $memo .= ' 释放订单冻结失败！';
        }

        error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n ".$order_bn."====释放订单冻结结果：".var_export($freeResult, 1)."\n", 3, DATA_DIR.'/delOrder.log');
        #更新订单
        $uData = array(
//            'status' => 'dead',
            'is_fail' => 'true',
            'archive' => '1',
            'edit_status' => 'true',
            'process_status' => 'cancel',
        );

        $uFilter = array(
            'order_id' => $ordInfo['order_id']
        );
        $res = $ordMdl->update($uData, $uFilter);
        if($res){
            $memo .= ' 订单状态、单号更新成功！';
            #更新支付单
            $pList = $paymentMdl->getList("*", array("order_id" => $ordInfo['order_id']));
            foreach($pList as $pk => $pv){
                $pData = array(
                    "trade_no" => 'd'.$pv['trade_no'],
                    "payment_bn" => 'd'.$pv['payment_bn']
                );
                $pFilter = array(
                    'order_id' => $ordInfo['order_id']
                );
                //$pres = $paymentMdl->update($pData,$pFilter);
                if($pres){
                    $memo .= ' 支付单'.$pv['payment_bn']."流水号更新成功！";
                }else{
                    $memo .= ' 支付单'.$pv['payment_bn']."流水号更新失败！";
                }
                error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n ".$order_bn."====支付单更新结果：".var_export($pres, 1)."\n", 3, DATA_DIR.'/delOrder.log');
            }
        }else{
            $memo .= ' 订单状态、单号更新失败！';
        }
        $oOperation_log->write_log('order_modify@ome',$ordInfo['order_id'],$memo);
        error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s')."\n ".$order_bn."====订单更新结果：".var_export($res, 1)."\n", 3, DATA_DIR.'/delOrder.log');
        error_log(__LINE__.'=>>时间：'.date('Y-m-d H:i:s').'--->'.$order_bn."====订单更新结果：".var_export($res, 1)."===释放冻结结果：".var_export($freeResult, 1)."\n", 3, DATA_DIR.'/delOrder2.log');
    }

    $i++;
}