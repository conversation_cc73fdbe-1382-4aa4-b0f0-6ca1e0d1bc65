<?php
error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$start_time = strtotime('2025-07-24 18:00:00');
$end_time = strtotime('2025-07-26 14:00:00');

//按照时间和店铺，查出有退款单数据，并且订单的支付状态不是 退款和部分退款的。
$db = kernel::database();

$sql = "select * from sdb_ome_refunds where download_time > $start_time and download_time< $end_time and shop_id in 
  ('784a27c9f4db2be0ca465891e1050430','7beafba193c76c7488e6d84b0f933da9','b8550c848eb398abee71c7bb34f6c890','fb1fbdb745364d6e6167309e2472371b')";

$refunds = $db->select($sql);
$order_bns = [];
$order_count = [];
foreach ($refunds as $refund_info){
    $refund_bn = $refund_info['refund_bn'];
    $order_id = $refund_info['order_id'];

    if ($refund_info['status'] == 'succ') {
        $order_info = $db->selectrow("select * from sdb_ome_orders where order_id = $order_id ");

        if($order_info && !in_array($order_info['pay_status'],['4','5'])){
            $order_bns[] = ['order_bn'=>$order_info['order_bn'],'pt'=>$order_info['pay_status']];

            if(!$order_count[$order_info['order_bn']]){
                $order_count[$order_info['order_bn']] = 0;
            }
            $order_count[$order_info['order_bn']] += 1;

            if($order_info['order_bn'] == '4953789000209569') {
                //参数改成和接口的一样
                $params = $refund_info;
                $params['refund_fee'] = $refund_info['money'];
                $params['update_order_payed'] = '1';
                $params['order'] = $order_info;
                if ($refund_info['return_id']) {
                    $params['refund_apply'] = $db->selectrow("select * from sdb_ome_refund_apply where return_id = " . $refund_info['return_id']);
                }

                $error_msg = '';
                $is_abnormal = false;
                kernel::single('erpapi_shop_response_process_refund')->_autoEditorder($params, $error_msg, $is_abnormal);

                echo "error:" . $error_msg . "order_bn:" . $order_info['order_bn'] . "\n\r";

                _updateOrder($params['order']['order_id'], $params['money']);
                kernel::single('invoice_process')->invoice_cancel($params['order']);
            }

        }

    }
}


function _updateOrder($orderId, $refundMoney)
{
    if (empty($orderId)) {
        return false;
    }

    //更新订单支付金额
    if ($refundMoney) {
        $sql = "update sdb_ome_orders set payed=IF((CAST(payed AS char)-IFNULL(0,cost_payment)-" . $refundMoney . ")>=0,payed-IFNULL(0,cost_payment)-" . $refundMoney . ",0)  where order_id=" . $orderId;
        kernel::database()->exec($sql);
    }

    //更新订单支付状态
    return kernel::single('ome_order_func')->update_order_pay_status($orderId);
}


echo json_encode($order_bns);
