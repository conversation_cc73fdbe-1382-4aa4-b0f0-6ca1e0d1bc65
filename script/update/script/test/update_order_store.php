<?php

error_reporting(0);

$domain = $argv[1];
if (empty(trim($domain))) {
    die('参数不能为空！');
}

set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_bns = ['3729826830538907904','3729826612489102592','3729826409375213568','3729826231747490048','3729825634185000960','3729825472023246080','3729825472617002496','3729825441882715136','3729825414223896064','3729825259121417472','3729825226841273600','3729824887074857472','3729824867275912448','3729824865436441600','3729824839574108160','3729824489975979520','3729824378037351424','3729824309551176704','3729824176752177920','3729824117005889536','3729824091891176448','3729824069466334720','3729823980177990400','3729823569794703872','3729823562194101760','3729823526101069824','3729823446836585984','3729823432443828736','3729823402027003904','3729823378014086656'];


$order_obj = app::get('ome')->model('orders');
$order_item_obj = app::get('ome')->model('order_items');
$orders = $order_obj->getList('*',['order_bn'=>$order_bns]);
$db = kernel::database();

$store_bn = 'FVSH0011';
$store_info = $db->selectrow("select * from sdb_o2o_store where store_bn = '$store_bn'");
if(!$store_info){
    exit("没有找到 $store_bn 的门店！");
}
$store_id = $store_info['store_id'];
echo 'store_id:'.$store_id."\n\r";

foreach ($orders as $order_info){
    $order_id = $order_info['order_id'];
    $order_bn = $order_info['order_bn'];
    echo 'start_order_bn:'.$order_bn."\n\r";

    //审核的取消发货单
    if(in_array($order_info['process_status'],['splitting','splited'])){
        $result = $order_obj->cancel_delivery($order_id);
        echo 'result:'.json_encode($result)."\n\r";
        if ($result['rsp'] != 'succ') {
            continue;
        }
    }



    //更改 fulfillment_store_id
    $db->query("update sdb_ome_order_items set fulfillment_store_id = $store_id where order_id = $order_id ");

    //释放库存
    $order_items = $order_item_obj->getList('product_id',['order_id'=>$order_id]);
    foreach ($order_items as $item_info){
        $product_id = $item_info['product_id'];
        kernel::single('ome_sync_product')->reset_freeze($product_id);
        //重置预占流水记录
        kernel::single('console_stock_freeze')->reset_stock_freeze($product_id);
    }



    echo 'end_order_bn:'.$order_bn."\n\r";
}