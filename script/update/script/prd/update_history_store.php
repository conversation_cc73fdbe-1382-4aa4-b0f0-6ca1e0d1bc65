<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

define('BASE_URL', 'https://oms.fvo2o.com/');

// 获取视频号的所有历史订单
$shop_id = '';

// 导入的字段
// 订单号,子订单号,子订单ID,商品ID,SKU ID,所属小镇,商家编码,商品货号,商品大码,商品数量,退单数量,商品单价,订单金额,推送金蝶金额,发货状态,支付状态,订单状态,发货物流公司,发货物流单号,发货物流公司名称,发货时间,商品名称,商品颜色,商品规格,商品图片地址

// 获取csv文件
$file = '18.csv';
$list = array_map('str_getcsv', file($file));

// 按照订单号组织数据
$orderData = [];
$payStatusMap = [];
$orderDataItems = [];
foreach ($list as $key => $row) {
    // 跳过标题行
    if ($key === 0) {
        continue;
    }

    $orderBn = $row[0]; // 订单号
    $subOrderBn = $row[1]; // 子订单号
    $subOrderId = $row[2]; // 子订单ID
    $goodsId = $row[3]; // 商品ID
    $skuId = $row[4]; // SKU ID
    $town = $row[5]; // 所属小镇
    $merchantCode = $row[6]; // 商家编码
    $itemNo = $row[7]; // 商家编码
    $largeCode = $row[8]; // 商品大码
    $goodsNum = $row[9]; // 商品数量
    $refundNum = $row[10]; // 退单数量
    $goodsPrice = $row[11]; // 商品单价
    $orderAmount = $row[12]; // 订单金额
    $kdAmount = $row[13]; // 推送金蝶金额
    $shipStatus = $row[14]; // 发货状态
    $payStatus = $row[15]; // 支付状态
    $orderStatus = $row[16]; // 订单状态
    $logiCompany = $row[17]; // 发货物流公司
    $logiNo = $row[18]; // 发货物流单号
    $logiCompanyName = $row[19]; // 发货物流公司名称
    $shipTime = $row[20]; // 发货时间
    $goodsName = $row[21]; // 商品名称
    $goodsColor = $row[22]; // 商品颜色
    $goodsSize = $row[23]; // 商品规格
    $goodsImage = $row[24]; // 商品图片地址

    $item = [
        'sku_id' => $skuId,
        'num' => $goodsNum,
        'history_oid' => $subOrderBn,
        'history_obj_id' => $subOrderId,
        'logi_no' => $logiNo,
        'pay_status' => $payStatus,
        'ship_status' => $shipStatus,
        'logi_company' => $logiCompany,
        'customerStoreCode' => $merchantCode, // 门店编码
        'itemNo' => $itemNo,
        'largeSize' => $largeCode,
        'color' => $goodsColor,
        'size' => $goodsSize,
        'image' => $goodsImage,
        'shipTime' => strtotime($shipTime),
        'kdAmount' => $kdAmount
    ];

    if (!isset($orderData[$orderBn])) {
        $orderData[$orderBn] = [];
    }

    $sql = "select i.fulfillment_store_id,s.store_id,i.item_id,o.order_id,i.bn,s.store_bn from sdb_ome_order_items i left join sdb_ome_order_objects b on i.obj_id=b.obj_id left join sdb_ome_orders o on i.order_id = o.order_id left join sdb_o2o_store s on i.fulfillment_store_id = s.store_id where o.order_bn = '{$orderBn}' and b.history_obj_id= '{$subOrderId}' and s.store_bn != '{$merchantCode}'";
    $info = kernel::database()->selectrow($sql);
    if ($info) {
        $storeInfo = app::get('o2o')->model('store')->dump(['store_bn' => $merchantCode], 'store_id,store_bn,branch_id');
        $info['old_store_id'] = $storeInfo['store_id'];
        $info['old_store_bn'] = $storeInfo['store_bn'];
        $info['old_branch_id'] = $storeInfo['branch_id'];
        $orderData[$orderBn][] = $info;
    }
}

kernel::database()->beginTransaction();
try {
foreach ($orderData as $orderBn => $info) {
    foreach ($info as $item) {
        $sql = "update sdb_ome_order_items set fulfillment_store_id = '{$item['old_store_id']}' where item_id = '{$item['item_id']}' and bn = '{$item['bn']}' and order_id = {$item['order_id']}";
        echo $sql . "\n";
        kernel::database()->exec($sql);

        // 判断是否有发货单
        $sql = "select d.delivery_id,d.delivery_bn from sdb_ome_delivery d left join sdb_ome_delivery_order do on d.delivery_id = do.delivery_id left join sdb_ome_delivery_items di on d.delivery_id = di.delivery_id where di.bn= '{$item['bn']}' and do.order_id = '{$item['order_id']}'";
        $deliveryInfo = kernel::database()->selectrow($sql);
        if ($deliveryInfo) {
            $sql = "update sdb_ome_delivery set branch_id = '{$item['old_branch_id']}' where delivery_id = '{$deliveryInfo['delivery_id']}'";
            echo $sql . "\n";
            kernel::database()->exec($sql);

            // 修改H5的
            $sql = "update sdb_wap_delivery set branch_id = '{$item['old_branch_id']}' where outer_delivery_bn = '{$deliveryInfo['delivery_bn']}'";
            echo $sql . "\n";
            kernel::database()->exec($sql);
        }

        $refunds = app::get('ome')->model('refunds')->getList('refund_id,refund_bn', ['order_id' => $item['order_id'], 'belong_store_id' => $item['store_id']]);
        if ($refunds) {
            foreach ($refunds as $refund) {
                $sql = "update sdb_ome_refunds set belong_store_id = '{$item['old_store_id']}' where refund_id = '{$refund['refund_id']}' and refund_bn = '{$refund['refund_bn']}' and belong_store_id = '{$item['store_id']}'";
                echo $sql . "\n";
                kernel::database()->exec($sql);
            }
        }

        // reship 是是否有值
        $sql = "select reship_id,reship_bn from sdb_ome_reship where order_id = '{$item['order_id']}' and belong_store_id = '{$item['store_id']}'";
        $reshipInfo = kernel::database()->selectrow($sql);
        if ($reshipInfo) {
            $sql = "update sdb_ome_reship set belong_store_id = '{$item['old_store_id']}',belong_store_bn = '{$item['old_store_bn']}',branch_id = '{$item['old_branch_id']}' where reship_id = '{$reshipInfo['reship_id']}' and reship_bn = '{$reshipInfo['reship_bn']}' and belong_store_id = '{$item['store_id']}'";
            echo $sql . "\n";

            $sql = "update sdb_ome_reship_items set branch_id = '{$item['old_branch_id']}' where reship_id = '{$reshipInfo['reship_id']}' and bn = '{$item['bn']}'";
            kernel::database()->exec($sql);
        }

        // return_product 是否有值
        $sql = "select return_id,return_bn from sdb_ome_return_product where order_id = '{$item['order_id']}' and belong_store_id = '{$item['store_id']}'";
        $returnProductInfo = kernel::database()->selectrow($sql);
        if ($returnProductInfo) {
            $sql = "update sdb_ome_return_product set belong_store_id = '{$item['old_store_id']}',belong_store_bn = '{$item['old_store_bn']}' where return_id = '{$returnProductInfo['return_id']}' and return_bn = '{$returnProductInfo['return_bn']}' and belong_store_id = '{$item['store_id']}'";
            echo $sql . "\n";
            kernel::database()->exec($sql);
        }

        $refund_apply_info = app::get('ome')->model('refund_apply')->getList('apply_id', ['order_id' => $item['order_id'], 'belong_store_id' => $item['store_id']]);
        if ($refund_apply_info) {
            foreach ($refund_apply_info as $refund_apply) {
                $sql = "update sdb_ome_refund_apply set belong_store_id = '{$item['old_store_id']}' where apply_id = '{$refund_apply['apply_id']}' and belong_store_id = '{$item['store_id']}'";
                echo $sql . "\n";
                kernel::database()->exec($sql);
            }
        }

        $sql = "select * from sdb_sales_sap_orders where source_bill_id='{$item['order_id']}'";
        $list = kernel::database()->select($sql);
        if ($list) {
            foreach ($list as $sapOrder) {
                $sql = "update sdb_sales_sap_order_items set shopId= '{$item['old_store_bn']}' where sap_id = {$sapOrder['sap_id']} and shopId = '{$item['store_bn']}'";
                echo $sql . "\n";
                kernel::database()->exec($sql);
            }
        }
    }
}
} catch (Exception $e) {
    kernel::database()->rollback();
    echo $e->getMessage();
}
kernel::database()->commit();
