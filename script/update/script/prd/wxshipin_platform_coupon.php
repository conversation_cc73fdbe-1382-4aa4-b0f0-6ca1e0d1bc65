<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

$order_bns = [
    '3729801722566939648',
];

$apiMdl = app::get('ome')->model('api_log');
$orderMdl = app::get('ome')->model('orders');
$couponMdl = app::get('ome')->model('order_coupon_wxshipin');

foreach ($order_bns as $order_bn) {
    # 获取订单详情
    $orderInfo = $orderMdl->dump(['order_bn' => $order_bn], '*');
    if (empty($orderInfo)) {
        echo '订单号' . $order_bn . '不存在' . PHP_EOL;
        continue;
    }

    $sql = <<<SQL
        SELECT `log_id`,params FROM `sdb_ome_api_log` WHERE `original_bn` =  '{$order_bn}' AND api_type = 'response' AND task_name like '创建订单%' 
        ORDER BY `createtime` ASC LIMIT 1
    SQL;
    $logList = kernel::database()->select($sql);
    if (empty($logList)) {
        continue;
    }

    $logInfo = current($logList);
    $sdf = json_decode($logInfo['params'], true);


    # 获取订单order_objects信息
    $sdf['order_objects'] = is_string($sdf['order_objects']) ? json_decode($sdf['order_objects'], true) : $sdf['order_objects'];
    $sdf['extend_field'] = is_string($sdf['extend_field']) ? json_decode($sdf['extend_field'], true) : $sdf['extend_field'];
    $sdf['member_info'] = is_string($sdf['member_info']) ? json_decode($sdf['member_info'], true) : $sdf['member_info'];

    $ext_data = array();
    $ext_data['shop_id'] = $orderInfo['shop_id'];
    $ext_data['shop_type'] = $orderInfo['shop_type'];
    $ext_data['createtime'] = $orderInfo['createtime'];
    $ext_data['order_bn'] = $orderInfo['order_bn'];
    $ext_data['price_info'] = $sdf['extend_field']['price_info'] ?? [];
    $ext_data['coupon_info'] = $sdf['extend_field']['coupon_info'] ?? [];
    $ext_data['openid'] = $sdf['member_info']['buyer_open_uid'];
    $ext_data['coupon_source'] = 'push';

    # 获取优惠券数据
    $result = kernel::single('ome_order_coupon')->couponDataFormat($sdf['order_objects'], $ext_data, $ext_data['shop_type']);
    if (empty($result['coupon_data'])) {
        continue;
    }

    $platform_coupon_amount = 0;
    foreach ($result['coupon_data'] as $key => $v) {
        $filter = [
            'order_id' => $orderInfo['order_id'],
            'oid' => $v['oid'],
            'type' => $v['type'],
            'coupon_id' => $v['coupon_id'],
            'coupon_type' => $v['coupon_type'],
        ];
        $couponInfo = $couponMdl->dump($filter, 'id');
        if (empty($couponInfo)) {
            $result['coupon_data'][$key]['order_id'] = $orderInfo['order_id'];
            $couponMdl->save($result['coupon_data'][$key]);
        }

        # 记录平台优惠金额
        $platform_coupon_amount = bcadd($platform_coupon_amount, $v['coupon_amount'], 2);
    }

    # 更新订单优惠金额
    if ($platform_coupon_amount > 0 && $orderInfo['pay_status'] == '3') {
        # 获取订单优惠金额
        $order_pmt_order = $orderInfo['pmt_order'];
        $diff_amount = bcsub($orderInfo['total_amount'], $orderInfo['payed'], 2);
        if (bccomp($diff_amount, $platform_coupon_amount, 2) == 0) {
            $updateData = [
                'pmt_order' => bcadd($orderInfo['pmt_order'], $platform_coupon_amount, 2),
                'discount' => bcadd($orderInfo['discount'], $platform_coupon_amount, 2),
                'total_amount' => bcsub($orderInfo['total_amount'], $platform_coupon_amount, 2),
                'pay_status' => '1',
            ];
            $orderMdl->update($updateData, array('order_id' => $orderInfo['order_id']));
        }

        # 推送esb
        $extend = [
            'filter_delete_item' => false
        ];
        $sdf = kernel::single('ome_sap_data_platform_' . erpapi_sap_func::getShopType($orderInfo['shop_type']))->get_order_sdf($orderInfo['order_id'], $extend);
        $result = kernel::single('erpapi_router_request')->set('sap', true)->order_push($sdf);
        if ($result['rsp'] == 'fail') {
            echo '订单号：' . $orderInfo['order_bn'] . ',推送sap失败：' . $result['msg'] . PHP_EOL;
            continue;
        }

        echo '订单号：' . $orderInfo['order_bn'] . ',推送成功' . PHP_EOL;
    }
}

echo 'end...' . PHP_EOL;
