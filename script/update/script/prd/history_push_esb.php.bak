<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

// 推送支付单到esb
$sql = "select * from `sdb_sales_sap_orders` where bill_type = 'payed' and sync_status = 'none' and bill_no = '3728876114149257984'";
$order = kernel::database()->selectrow($sql);
if ($order) {
    $order_id = $order['bill_id'];
    push_esb($order_id, $order['sap_id']);
}

function push_esb($order_id, $sap_id)
{
    $orderInfo = app::get('ome')->model('orders')->dump(array('order_id' => $order_id), 'shop_type,is_history,order_bn,total_amount');

    if ($orderInfo['total_amount'] <= 0) {
        echo "订单金额为0, 不推送\n";
        return;
    }

    if ($orderInfo['is_history'] != 'true') {
        echo "非历史订单, 不推送\n";
        return;
    }

    if ($orderInfo['shop_type'] != 'wxshipin') {
        echo "非微信小程序订单, 不推送\n";
        return;
    }

    echo "开始推送订单: " . $orderInfo['order_bn'] . "\n";

    $sql = "update `sdb_sales_sap_orders` set sync_status = 'fail' where sap_id = $sap_id and bill_type = 'payed' and bill_id = '$order_id' and sync_status = 'none'";
    kernel::database()->exec($sql);

    $extend = [
        'filter_delete_item' => false
    ];
    $sdf = kernel::single('ome_sap_data_platform_' . $orderInfo['shop_type'])->get_order_sdf($order_id, $extend);
    $result = kernel::single('erpapi_router_request')->set('sap', true)->order_push($sdf);
    if ($result['rsp'] != 'succ') {
        echo "订单推送失败\n";
        return;
    }

    // 推送发货单
    $sql = "select * from `sdb_sales_sap_orders` where source_bill_id = '$order_id' and bill_type = 'delivery' and sync_status = 'none'";
    $deliveryList = kernel::database()->select($sql);
    if (!empty($deliveryList)) {
        foreach ($deliveryList as $delivery) {

            $sql = "update `sdb_sales_sap_orders` set sync_status = 'fail' where sap_id = {$delivery['sap_id']} and bill_type = 'delivery' and bill_id = '{$delivery['bill_id']}' and sync_status = 'none'";
            kernel::database()->exec($sql);

            $delivery_id = $delivery['bill_id'];
            $sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_delivery_sdf($delivery_id);
            $result = kernel::single('erpapi_router_request')->set('sap', true)->delivery_push($sdf);
        }
    }

    // 推送退款单
    $sql = "select * from `sdb_sales_sap_orders` where source_bill_id = '$order_id' and bill_type = 'refund' and sync_status = 'none'";
    $refundList = kernel::database()->select($sql);
    if (!empty($refundList)) {
        foreach ($refundList as $refund) {
            $sql = "update `sdb_sales_sap_orders` set sync_status = 'fail' where sap_id = {$refund['sap_id']} and bill_type = 'refund' and bill_id = '{$refund['bill_id']}' and sync_status = 'none'";
            kernel::database()->exec($sql);

            $apply_id = $refund['bill_id'];
            $sdf = kernel::single('ome_sap_data_platform_' . $orderInfo['shop_type'])->get_refund_sdf($apply_id);
            $result = kernel::single('erpapi_router_request')->set('sap', true)->refund_push($sdf);
        }
    }

    // 推送退货单
    $sql = "select * from `sdb_sales_sap_orders` where source_bill_id = '$order_id' and bill_type = 'return' and sync_status = 'none'";
    $returnList = kernel::database()->select($sql);
    if (!empty($returnList)) {
        foreach ($returnList as $return) {
            $sql = "update `sdb_sales_sap_orders` set sync_status = 'fail' where sap_id = {$return['sap_id']} and bill_type = 'return' and bill_id = '{$return['bill_id']}' and sync_status = 'none'";
            kernel::database()->exec($sql);

            $reship_id = $return['bill_id'];
            $sdf = kernel::single('ome_sap_data_platform_wxshipin')->get_reship_sdf($reship_id);
            kernel::single('erpapi_router_request')->set('sap', true)->reship_push($sdf);
        }
    }
}