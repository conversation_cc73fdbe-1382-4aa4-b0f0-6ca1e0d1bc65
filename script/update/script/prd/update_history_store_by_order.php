<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

define('BASE_URL', 'https://oms.fvo2o.com/');

// 获取视频号的所有历史订单
$shop_id = '';

// 导入的字段
// 订单号,子订单号,子订单ID,商品ID,SKU ID,所属小镇,商家编码,商品货号,商品大码,商品数量,退单数量,商品单价,订单金额,推送金蝶金额,发货状态,支付状态,订单状态,发货物流公司,发货物流单号,发货物流公司名称,发货时间,商品名称,商品颜色,商品规格,商品图片地址

$orderBns = array(
    '3729509876592157185',
    '3729509852218274560',
    '3729509849176874752',
    '3729509846811288320',
    '3729509834486323712',
    '3729509834575715584',
    '3729509834575715584',
    '3729509827798508800',
    '3729509805763992832',
    '3729509792305784832',
    '3729509792305784832',
    '3729509792305784832',
    '3729509766085879040',
    '3729509755566039552',
    '3729509742098652160',
    '3729509735407162368',
    '3729509709968187648',
    '3729509691386639360',
    '3729509691386639360',
    '3729509608550708224',
    '3729509601110537472',
    '3729509593260102656',
    '3729509559056603648',
    '3729509522965935104',
    '3729509520960795904',
    '3729509518372381184',
    '3729509509997404416',
    '3729509499112399104',
    '3729509488761642752',
    '3729509487818185984',
    '3729509473647997184',
    '3729509461875636224',
    '3729509455249683200',
    '3729509444088897280',
    '3729509429853435648',
    '3729509418670628096',
    '3729509350979811328',
    '3729509184744070400',
    '3729509181694022656',
    '3729509166834654720',
    '3729509144900539392',
    '3729509141232096256',
    '3729509141232096256',
    '3729509126000748032',
    '3729509087045628672',
    '3729509070919572736',
    '3729509030514795776',
    '3729508901319226368',
    '3729508879887377152',
    '3729508875050563072',
    '3729508875050563072',
    '3729508875050563072',
    '3729508857374973696',
    '3729508813997748224',
    '3729508805970374656',
    '3729508792042139904',
    '3729508763831514624',
    '3729508759116585472',
    '3729508757060326400',
    '3729508720618117632',
    '3729508596552441088',
    '3729508592172539392',
    '3729508577704286720',
    '3729508518673917440',
    '3729508500006122496',
    '3729508496840990976',
    '3729508482988253440',
    '3729508469294642944',
    '3729508461786571008',
    '3729508426329241344',
    '3729508420424972032',
    '3729508391852055552',
    '3729508385146152960',
    '3729508377869563904',
    '3729508374547673088',
    '3729508370316927488',
    '3729508363172979456',
    '3729508344513836800',
    '3729508281508835328',
    '3729508281508835328',
    '3729508281508835328',
    '3729508216357922816',
    '3729508186049097728',
    '3729508186049097728',
    '3729508157806752000',
    '3729508152248513792',
    '3729508152248513792',
    '3729508142376689920',
    '3729508137401725184',
    '3729508134409085440',
    '3729508130919692288',
    '3729508130919692288',
    '3729508124370810368',
    '3729508119882903808',
    '3729508110722016512',
    '3729508091576066816',
    '3729508091687740928',
    '3729508091687740928',
    '3729508088525239552',
    '3729507947287294976',
    '3729507900100322816',
    '3729507850700857856',
    '3729507812445395968',
    '3729507812445395968',
    '3729507784468866560',
    '3729507784468866560',
    '3729507781119193088',
    '3729507774864700416',
    '3729507774864700416',
    '3729507774864700416',
    '3729507773798031104',
    '3729507751064116224',
    '3729507751064116224',
    '3729507721889331200',
    '3729507717532492544',
    '3729507663462668800',
    '3729507627953697536',
    '3729507584545791744',
    '3729507579093982464',
    '3729507575791235328',
    '3729507575791235328',
    '3729507499494225152',
    '3729507493673056256',
    '3729507370768408320',
    '3729507309713240064',
    '3729507145372016128',
    '3729507145372016128',
    '3729507135500203008',
    '3729507091619652352',
    '3729507091619652352',
    '3729507057593097472',
    '3729506995107410944',
    '3729506994546422528',
    '3729506884939822336',
    '3729506860166157312',
    '3729506854194263552',
    '3729506843318690304',
    '3729506809250461696',
    '3729506801153881088',
    '3729506789566323712',
    '3729506779152658688',
    '3729506776096579840',
    '3729506775284462848',
    '3729506758176938496',
    '3729506751227240192',
    '3729506736755318272',
    '3729506689262695168',
    '3729506688627258112',
    '3729506664724177152',
    '3729506651917917952',
    '3729506628371100416',
    '3729506596090423296',
    '3729506584348204288',
    '3729506576167745536',
    '3729506560666379520',
    '3729506532280641025',
    '3729506532280641025',
    '3729506526747827712',
    '3729506491346334464',
    '3729506491346334464',
    '3729506470472326144',
    '3729506465989664256',
    '3729506463873119744',
    '3729506460321326592',
    '3729506449380749568',
    '3729506437337849088',
    '3729506418013910784',
    '3729506353794654976',
    '3729506340796506368',
    '3729506309041435648',
    '3729506292607886592',
    '3729506292607886592',
    '3729506282145977344',
    '3729506276741363712',
    '3729506231335401472',
    '3729506227257745920',
    '3729506226211794944',
    '3729506225714502144',
    '3729506224319897344',
    '3729506223314575616',
    '3729506222364045568',
    '3729506222301649920',
    '3729506222456574976',
    '3729506222517130240',
    '3729506222458936832',
    '3729506221842900992',
    '3729506221100768512',
    '3729506221880915968',
    '3729506220519340032',
    '3729506221098147072',
    '3729506221286109952',
    '3729506220265579776',
    '3729506220841509376',
    '3729506221030776064',
    '3729506219403653376',
    '3729506207526166016',
);

$orderBns = array_unique($orderBns);
foreach ($orderBns as $orderBn) {
    $sql = "select order_id from sdb_ome_orders where order_bn = '{$orderBn}'";
    $orderInfo = kernel::database()->selectrow($sql);
    if ($orderInfo) {
        $order_id = $orderInfo['order_id'];
    }

    $sql = "select * from sdb_sales_sap_orders where source_bill_id='{$order_id}'";
    $list = kernel::database()->select($sql);
    if ($list) {
        foreach ($list as $sapOrder) {
            $sql = "update sdb_sales_sap_order_items set shopId= 'FVSH065C' where sap_id = {$sapOrder['sap_id']} and shopId = 'FVSH5479'";
            echo $sql . "\n";
            kernel::database()->exec($sql);
        }
    }

    $oldStore = app::get('o2o')->model('store')->dump(['store_bn' => 'FVSH5479'], 'store_id,store_bn,branch_id');
    $newStore = app::get('o2o')->model('store')->dump(['store_bn' => 'FVSH065C'], 'store_id,store_bn,branch_id');

    $sql = "select reship_id,reship_bn from sdb_ome_reship where order_id = $order_id and belong_store_id = '{$oldStore['store_id']}'";
    $reshipInfo = kernel::database()->selectrow($sql);
    if ($reshipInfo) {
        $sql = "update sdb_ome_reship set belong_store_id = '{$newStore['store_id']}',belong_store_bn = '{$newStore['store_bn']}',branch_id = '{$newStore['branch_id']}' where reship_id = '{$reshipInfo['reship_id']}' and reship_bn = '{$reshipInfo['reship_bn']}' and belong_store_id = '{$oldStore['store_id']}'";
        echo $sql . "\n";
        kernel::database()->exec($sql);
    }
}
