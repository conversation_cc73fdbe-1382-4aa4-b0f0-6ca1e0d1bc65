<?php

error_reporting(0);
set_time_limit(0);
require_once(dirname(__FILE__) . '/../../../lib/init.php');
cachemgr::init(false);

// 获取视频号的所有历史订单
$shop_id = '';


$sql = "select o2.order_id from sdb_ome_order_items i left join sdb_ome_order_objects o on i.obj_id = o.obj_id left join sdb_ome_orders o2 on o.order_id = o2.order_id where o.is_history='true' and i.nums > 0";
$list = kernel::database()->select($sql);

$orderIds = array_column($list, 'order_id');

foreach($orderIds as $orderId) {
    $orderInfo = app::get('ome')->model('orders')->db_dump(['order_id' => $orderId], 'order_id,total_amount,order_bn');

    $sql = "select sum(divide_order_fee) as divide_order_fee from sdb_ome_order_items where order_id = {$orderId}";
    $divideOrderFee = kernel::database()->selectrow($sql);
    if ($divideOrderFee['divide_order_fee'] < $orderInfo['total_amount']) {

        $sql = "select * from sdb_ome_order_items where " 

    }
}
